<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展程序快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 扩展程序快速测试</h1>
        <p>这个页面可以快速测试系统音频录制扩展程序是否正常工作。</p>
        
        <div id="status" class="status info">
            正在检测扩展程序...
        </div>
        
        <div>
            <button class="btn-primary" onclick="checkExtension()">重新检测扩展程序</button>
            <button class="btn-secondary" onclick="testMessage()">发送测试消息</button>
            <button class="btn-success" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="log" id="log">等待检测结果...
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function setStatus(text, type = 'info') {
            statusElement.textContent = text;
            statusElement.className = `status ${type}`;
        }
        
        function checkExtension() {
            clearLog();
            setStatus('正在检测扩展程序...', 'info');
            
            addLog('🔍 开始扩展程序检测');
            addLog(`🌐 当前页面: ${window.location.href}`);
            addLog(`🔧 User Agent: ${navigator.userAgent}`);
            
            // 检查Chrome环境
            if (!window.chrome || !window.chrome.runtime) {
                setStatus('❌ 不在Chrome浏览器中', 'error');
                addLog('❌ 当前环境不支持Chrome扩展程序');
                return;
            }
            
            addLog('✅ Chrome环境检测通过');
            
            // 监听扩展程序消息
            window.addEventListener('message', function handler(event) {
                if (event.source !== window) return;
                
                const data = event.data;
                if (!data || !data.type) return;
                
                addLog(`📨 收到消息: ${data.type}`);
                addLog(`📋 消息内容: ${JSON.stringify(data, null, 2)}`);
                
                switch (data.type) {
                    case 'contentScriptReady':
                        setStatus('✅ 扩展程序已连接！', 'success');
                        addLog('🎉 内容脚本已加载并准备就绪');
                        break;
                        
                    case 'pageSupport':
                        if (data.supported) {
                            setStatus('✅ 页面支持音频录制', 'success');
                        } else {
                            setStatus('❌ 页面不支持音频录制', 'error');
                        }
                        addLog(`📋 支持详情: ${JSON.stringify(data)}`);
                        break;
                }
            });
            
            // 发送检测消息
            addLog('📤 发送检测消息...');
            setTimeout(() => {
                window.postMessage({ type: 'checkPageSupport' }, '*');
                addLog('📤 已发送页面支持检查消息');
            }, 1000);
            
            setTimeout(() => {
                if (statusElement.textContent.includes('检测中')) {
                    setStatus('⚠️ 扩展程序未响应，请检查是否已安装', 'warning');
                    addLog('⚠️ 扩展程序可能在5秒内未响应，请手动检查');
                }
            }, 6000);
        }
        
        function testMessage() {
            addLog('📤 发送测试消息到扩展程序...');
            window.postMessage({ type: 'getRecordingStatus' }, '*');
        }
        
        // 页面加载完成后自动检测
        window.addEventListener('load', () => {
            setTimeout(checkExtension, 1000);
        });
    </script>
</body>
</html>