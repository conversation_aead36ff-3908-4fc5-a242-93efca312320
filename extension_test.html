<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展程序测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button-primary {
            background-color: #007bff;
            color: white;
        }
        .button-danger {
            background-color: #dc3545;
            color: white;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 系统音频录制扩展程序测试</h1>
        
        <div class="status info">
            <h3>🔍 扩展程序状态检测</h3>
            <p id="extensionStatus">正在检测扩展程序...</p>
        </div>
        
        <div class="status info">
            <h3>📄 页面支持状态</h3>
            <p id="pageSupport">正在检查页面支持...</p>
        </div>
        
        <div class="status info">
            <h3>🎙️ 录制控制</h3>
            <button id="startRecording" class="button button-primary" disabled>开始录制</button>
            <button id="stopRecording" class="button button-danger" disabled>停止录制</button>
            <p id="recordingStatus">准备就绪</p>
        </div>
        
        <div class="status info">
            <h3>📋 消息日志</h3>
            <div id="messageLog" class="log"></div>
            <button id="clearLog" class="button">清空日志</button>
        </div>
        
        <div class="status info">
            <h3>📝 使用说明</h3>
            <ol>
                <li>确保已安装系统音频录制扩展程序</li>
                <li>等待扩展程序状态显示为"已连接"</li>
                <li>点击"开始录制"按钮测试音频捕获</li>
                <li>观察消息日志中的通信情况</li>
            </ol>
        </div>
    </div>

    <script>
        // 测试页面脚本
        let extensionConnected = false;
        let isRecording = false;
        let messageLog = [];
        
        const elements = {
            extensionStatus: document.getElementById('extensionStatus'),
            pageSupport: document.getElementById('pageSupport'),
            recordingStatus: document.getElementById('recordingStatus'),
            startButton: document.getElementById('startRecording'),
            stopButton: document.getElementById('stopRecording'),
            messageLog: document.getElementById('messageLog'),
            clearLog: document.getElementById('clearLog')
        };
        
        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            messageLog.push(logEntry);
            
            const logElement = document.createElement('div');
            logElement.textContent = logEntry;
            logElement.style.color = type === 'error' ? '#721c24' : type === 'success' ? '#155724' : '#0c5460';
            
            elements.messageLog.appendChild(logElement);
            elements.messageLog.scrollTop = elements.messageLog.scrollHeight;
        }
        
        // 更新扩展程序状态
        function updateExtensionStatus(connected, message) {
            extensionConnected = connected;
            elements.extensionStatus.textContent = message;
            elements.extensionStatus.className = connected ? 'status success' : 'status error';
            
            if (connected) {
                elements.startButton.disabled = false;
                addLog('✅ 扩展程序已连接', 'success');
            } else {
                elements.startButton.disabled = true;
                addLog('❌ 扩展程序未连接', 'error');
            }
        }
        
        // 检查扩展程序
        function checkExtension() {
            addLog('🔍 开始检查扩展程序...');
            
            // 检查Chrome API
            if (!window.chrome || !window.chrome.runtime) {
                updateExtensionStatus(false, '❌ 不在Chrome浏览器中');
                return;
            }
            
            addLog('✅ Chrome浏览器检测通过');
            
            // 发送测试消息
            window.postMessage({ type: 'checkPageSupport' }, '*');
            addLog('📤 发送页面支持检查消息');
            
            // 设置超时
            setTimeout(() => {
                if (!extensionConnected) {
                    updateExtensionStatus(false, '❌ 扩展程序未响应');
                    addLog('⏰ 扩展程序响应超时', 'error');
                }
            }, 3000);
        }
        
        // 开始录制
        function startRecording() {
            if (!extensionConnected) {
                addLog('❌ 扩展程序未连接，无法开始录制', 'error');
                return;
            }
            
            addLog('🎵 发送开始录制消息...');
            window.postMessage({ 
                type: 'startRecording', 
                sourceType: 'tab' 
            }, '*');
            
            elements.recordingStatus.textContent = '正在启动录制...';
            elements.startButton.disabled = true;
            elements.stopButton.disabled = false;
        }
        
        // 停止录制
        function stopRecording() {
            addLog('⏹️ 发送停止录制消息...');
            window.postMessage({ type: 'stopRecording' }, '*');
            
            elements.recordingStatus.textContent = '正在停止录制...';
            elements.startButton.disabled = false;
            elements.stopButton.disabled = true;
        }
        
        // 清空日志
        function clearLog() {
            elements.messageLog.innerHTML = '';
            messageLog = [];
            addLog('📝 日志已清空');
        }
        
        // 监听来自扩展程序的消息
        function handleMessage(event) {
            if (event.source !== window) return;
            
            const data = event.data;
            if (!data || !data.type) return;
            
            addLog(`📨 收到消息: ${JSON.stringify(data)}`);
            
            switch (data.type) {
                case 'contentScriptReady':
                    updateExtensionStatus(true, '✅ 扩展程序已连接');
                    addLog('🎉 内容脚本已准备就绪', 'success');
                    break;
                    
                case 'pageSupport':
                    if (data.supported) {
                        elements.pageSupport.textContent = '✅ 当前页面支持音频录制';
                        elements.pageSupport.className = 'status success';
                        addLog('✅ 页面支持检查通过', 'success');
                    } else {
                        elements.pageSupport.textContent = `❌ 页面不支持: ${data.error || '未知原因'}`;
                        elements.pageSupport.className = 'status error';
                        addLog('❌ 页面不支持音频录制', 'error');
                    }
                    break;
                    
                case 'recordingStarted':
                    isRecording = true;
                    elements.recordingStatus.textContent = '🎵 正在录制...';
                    elements.startButton.disabled = true;
                    elements.stopButton.disabled = false;
                    addLog('🎵 录制已开始', 'success');
                    break;
                    
                case 'recordingStopped':
                    isRecording = false;
                    elements.recordingStatus.textContent = '⏹️ 录制已停止';
                    elements.startButton.disabled = false;
                    elements.stopButton.disabled = true;
                    addLog('⏹️ 录制已停止', 'success');
                    break;
                    
                case 'recordingResponse':
                    if (data.success) {
                        addLog(`✅ ${data.action} 操作成功`, 'success');
                    } else {
                        addLog(`❌ ${data.action} 操作失败: ${data.error}`, 'error');
                    }
                    break;
                    
                case 'audioChunk':
                    addLog('🎵 收到音频数据块');
                    break;
                    
                default:
                    addLog(`❓ 未知消息类型: ${data.type}`);
            }
        }
        
        // 绑定事件
        elements.startButton.addEventListener('click', startRecording);
        elements.stopButton.addEventListener('click', stopRecording);
        elements.clearLog.addEventListener('click', clearLog);
        
        // 监听消息
        window.addEventListener('message', handleMessage);
        
        // 页面加载完成后检查扩展程序
        window.addEventListener('load', () => {
            addLog('🚀 页面加载完成，开始检查扩展程序...');
            setTimeout(checkExtension, 1000);
        });
        
        // 初始化
        addLog('📝 测试页面已加载');
    </script>
</body>
</html>