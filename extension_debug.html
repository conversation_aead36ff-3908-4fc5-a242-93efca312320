<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展程序详细调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #4299e1;
        }
        .log-entry.error {
            border-left-color: #f56565;
        }
        .log-entry.warning {
            border-left-color: #ed8936;
        }
        .log-entry.success {
            border-left-color: #48bb78;
        }
        button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3182ce;
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f7fafc;
        }
        .icon {
            font-size: 20px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 扩展程序详细调试工具</h1>
        
        <div class="section">
            <h2>📊 扩展程序状态检查</h2>
            <div id="extensionStatus">正在检查...</div>
            <button onclick="checkExtensionStatus()">重新检查</button>
        </div>

        <div class="section">
            <h2>🔧 Chrome API 检查</h2>
            <div id="chromeApiStatus">正在检查...</div>
            <button onclick="checkChromeAPI()">重新检查</button>
        </div>

        <div class="section">
            <h2>📡 通信测试</h2>
            <div id="communicationTest">准备测试...</div>
            <button onclick="testCommunication()">开始通信测试</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="section">
            <h2>📝 实时日志</h2>
            <div class="log-container" id="logContainer">
                <div class="log-entry">等待日志消息...</div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 手动测试</h2>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="testRecording()">测试录制功能</button>
            <button onclick="checkPageSupport()">检查页面支持</button>
            <div id="manualTestResults"></div>
        </div>

        <div class="section">
            <h2>📋 扩展程序信息</h2>
            <div id="extensionInfo">加载中...</div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let startTime = Date.now();

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="icon">${getIcon(type)}</span>[${timestamp}] ${message}`;
            
            const container = document.getElementById('logContainer');
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            
            // 限制日志数量
            if (container.children.length > 100) {
                container.removeChild(container.firstChild);
            }
        }

        function getIcon(type) {
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'debug': '🔍'
            };
            return icons[type] || '📝';
        }

        // 检查扩展程序状态
        function checkExtensionStatus() {
            log('开始检查扩展程序状态...', 'info');
            const statusDiv = document.getElementById('extensionStatus');
            
            try {
                if (!window.chrome || !window.chrome.runtime) {
                    statusDiv.innerHTML = '<div class="status error">❌ 不在Chrome浏览器中</div>';
                    log('不在Chrome浏览器中', 'error');
                    return;
                }
                
                log('Chrome浏览器检测通过', 'success');
                
                // 检查是否有扩展程序响应
                const checkTimeout = setTimeout(() => {
                    statusDiv.innerHTML = '<div class="status warning">⚠️ 扩展程序未响应</div>';
                    log('扩展程序未响应（超时）', 'warning');
                }, 5000);
                
                // 监听扩展程序响应
                const messageHandler = (event) => {
                    if (event.source !== window) return;
                    const data = event.data;
                    if (!data || !data.type) return;
                    
                    clearTimeout(checkTimeout);
                    log(`收到扩展程序消息: ${data.type}`, 'success');
                    
                    if (data.type === 'contentScriptReady') {
                        statusDiv.innerHTML = '<div class="status success">✅ 扩展程序已连接</div>';
                        log('扩展程序已连接', 'success');
                    }
                };
                
                window.addEventListener('message', messageHandler);
                
                // 发送测试消息
                setTimeout(() => {
                    log('发送测试消息到扩展程序...', 'info');
                    window.postMessage({ type: 'checkPageSupport' }, '*');
                }, 1000);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 检查失败: ${error.message}</div>`;
                log(`检查失败: ${error.message}`, 'error');
            }
        }

        // 检查Chrome API
        function checkChromeAPI() {
            log('开始检查Chrome API...', 'info');
            const apiDiv = document.getElementById('chromeApiStatus');
            let result = '<div class="status info">📊 Chrome API 检查结果:</div>';
            
            try {
                // 基础检查
                if (window.chrome) {
                    result += '<div class="status success">✅ window.chrome 可用</div>';
                    log('window.chrome 可用', 'success');
                } else {
                    result += '<div class="status error">❌ window.chrome 不可用</div>';
                    log('window.chrome 不可用', 'error');
                }
                
                // Runtime API
                if (window.chrome && window.chrome.runtime) {
                    result += '<div class="status success">✅ chrome.runtime 可用</div>';
                    log('chrome.runtime 可用', 'success');
                } else {
                    result += '<div class="status error">❌ chrome.runtime 不可用</div>';
                    log('chrome.runtime 不可用', 'error');
                }
                
                // Tabs API
                if (window.chrome && window.chrome.tabs) {
                    result += '<div class="status success">✅ chrome.tabs 可用</div>';
                    log('chrome.tabs 可用', 'success');
                } else {
                    result += '<div class="status warning">⚠️ chrome.tabs 不可用（正常，内容脚本中不可用）</div>';
                    log('chrome.tabs 不可用', 'warning');
                }
                
                // 扩展程序ID
                if (window.chrome && window.chrome.runtime && window.chrome.runtime.id) {
                    result += `<div class="status success">✅ 扩展程序ID: ${window.chrome.runtime.id}</div>`;
                    log(`扩展程序ID: ${window.chrome.runtime.id}`, 'success');
                } else {
                    result += '<div class="status warning">⚠️ 扩展程序ID不可用</div>';
                    log('扩展程序ID不可用', 'warning');
                }
                
            } catch (error) {
                result += `<div class="status error">❌ API检查失败: ${error.message}</div>`;
                log(`API检查失败: ${error.message}`, 'error');
            }
            
            apiDiv.innerHTML = result;
        }

        // 通信测试
        function testCommunication() {
            log('开始通信测试...', 'info');
            const testDiv = document.getElementById('communicationTest');
            testDiv.innerHTML = '<div class="status info">📡 正在测试通信...</div>';
            
            let receivedResponses = 0;
            const expectedResponses = 3;
            
            const messageHandler = (event) => {
                if (event.source !== window) return;
                const data = event.data;
                if (!data || !data.type) return;
                
                receivedResponses++;
                log(`通信测试收到消息: ${data.type}`, 'success');
                
                if (receivedResponses >= expectedResponses) {
                    testDiv.innerHTML = '<div class="status success">✅ 通信测试完成</div>';
                    log('通信测试完成', 'success');
                }
            };
            
            window.addEventListener('message', messageHandler);
            
            // 发送测试消息
            setTimeout(() => {
                log('发送页面支持检查...', 'info');
                window.postMessage({ type: 'checkPageSupport' }, '*');
            }, 1000);
            
            setTimeout(() => {
                log('发送录制状态查询...', 'info');
                window.postMessage({ type: 'getRecordingStatus' }, '*');
            }, 2000);
            
            setTimeout(() => {
                log('发送测试消息...', 'info');
                window.postMessage({ type: 'testMessage', data: 'hello' }, '*');
            }, 3000);
            
            // 超时检查
            setTimeout(() => {
                if (receivedResponses === 0) {
                    testDiv.innerHTML = '<div class="status error">❌ 通信测试失败 - 无响应</div>';
                    log('通信测试失败 - 无响应', 'error');
                } else if (receivedResponses < expectedResponses) {
                    testDiv.innerHTML = `<div class="status warning">⚠️ 通信测试部分完成 (${receivedResponses}/${expectedResponses})</div>`;
                    log(`通信测试部分完成 (${receivedResponses}/${expectedResponses})`, 'warning');
                }
            }, 8000);
        }

        // 手动测试函数
        function sendTestMessage() {
            log('手动发送测试消息...', 'info');
            window.postMessage({ type: 'testMessage', data: '手动测试' }, '*');
        }

        function testRecording() {
            log('测试录制功能...', 'info');
            window.postMessage({ type: 'startRecording', sourceType: 'tab' }, '*');
        }

        function checkPageSupport() {
            log('检查页面支持...', 'info');
            window.postMessage({ type: 'checkPageSupport' }, '*');
        }

        function clearLogs() {
            const container = document.getElementById('logContainer');
            container.innerHTML = '<div class="log-entry">日志已清空</div>';
            log('日志已清空', 'info');
        }

        // 扩展程序信息
        function updateExtensionInfo() {
            const infoDiv = document.getElementById('extensionInfo');
            let info = '<div class="status info">📋 扩展程序信息:</div>';
            
            // 当前页面信息
            info += `<div><strong>当前页面:</strong> ${window.location.href}</div>`;
            info += `<div><strong>页面标题:</strong> ${document.title}</div>`;
            info += `<div><strong>用户代理:</strong> ${navigator.userAgent}</div>`;
            info += `<div><strong>测试开始时间:</strong> ${new Date(startTime).toLocaleString()}</div>`;
            info += `<div><strong>当前时间:</strong> ${new Date().toLocaleString()}</div>`;
            
            // Chrome信息
            if (window.chrome) {
                info += '<div><strong>Chrome版本:</strong> ' + navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '未知' + '</div>';
            }
            
            infoDiv.innerHTML = info;
        }

        // 全局消息监听器
        window.addEventListener('message', (event) => {
            if (event.source !== window) return;
            const data = event.data;
            if (!data || !data.type) return;
            
            log(`收到消息: ${JSON.stringify(data)}`, 'debug');
        });

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('调试工具页面加载完成', 'success');
            checkExtensionStatus();
            checkChromeAPI();
            updateExtensionInfo();
            
            // 定期更新信息
            setInterval(updateExtensionInfo, 5000);
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            log(`页面错误: ${event.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });

        console.log('🔍 扩展程序调试工具已加载');
        console.log('💡 提示: 打开开发者工具查看更多日志信息');
    </script>
</body>
</html>