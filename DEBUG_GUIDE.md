# 🔍 扩展程序调试指南

## 问题诊断步骤

### 第1步：确认扩展程序安装

1. **打开Chrome扩展程序页面**
   - 访问 `chrome://extensions/`
   - 确保开启了"开发者模式"

2. **检查扩展程序状态**
   - 找到"系统音频录制助手"
   - 确认状态为"已启用"
   - 查看是否有错误提示

### 第2步：使用调试工具

1. **打开调试页面**
   - 在浏览器中打开 `extension_debug.html`
   - 查看各个状态检查的结果

2. **检查控制台日志**
   - 按 F12 打开开发者工具
   - 切换到 Console 标签
   - 查看是否有扩展程序相关的日志

### 第3步：检查扩展程序后台

1. **查看扩展程序后台**
   - 在扩展程序页面点击"服务工作线程"
   - 查看后台脚本的日志
   - 确认没有错误

2. **检查内容脚本**
   - 在页面控制台中查看是否有内容脚本的日志
   - 应该看到 "🎯 系统音频录制助手内容脚本已加载"

### 第4步：验证通信流程

1. **检查消息传递**
   - 在调试页面点击"开始通信测试"
   - 观察是否有消息响应
   - 检查日志中的消息流

2. **手动测试**
   - 点击"发送测试消息"
   - 点击"检查页面支持"
   - 观察响应情况

## 常见问题及解决方案

### 问题1：扩展程序未加载
**症状：** 调试页面显示"扩展程序未响应"

**解决方案：**
1. 确认扩展程序已正确安装
2. 刷新页面
3. 重新加载扩展程序（在扩展程序页面点击刷新按钮）

### 问题2：内容脚本未注入
**症状：** 控制台没有看到内容脚本加载日志

**解决方案：**
1. 检查 manifest.json 中的 content_scripts 配置
2. 确认当前页面URL在 matches 范围内
3. 刷新页面

### 问题3：后台脚本无响应
**症状：** 发送消息到后台脚本没有响应

**解决方案：**
1. 检查扩展程序是否有错误
2. 查看服务工作线程的控制台
3. 重新加载扩展程序

### 问题4：权限问题
**症状：** 权限相关错误

**解决方案：**
1. 确认扩展程序有必要的权限
2. 检查 host_permissions 配置
3. 确认页面URL在允许的范围内

## 调试日志说明

### 内容脚本日志
- `🎯 系统音频录制助手内容脚本已加载` - 内容脚本成功加载
- `📨 内容脚本收到后台消息` - 收到来自后台脚本的消息
- `🌐 内容脚本收到网页消息` - 收到来自网页的消息
- `📤 发送contentScriptReady消息到网页` - 向网页发送准备就绪消息

### 后台脚本日志
- `📨 后台脚本收到消息` - 收到来自内容脚本的消息
- `📤 发送者信息` - 消息发送者信息
- `🔧 标签页ID` - 标签页ID信息

### 网页日志
- `📨 收到扩展程序消息` - 收到来自扩展程序的消息
- `🎉 内容脚本已准备就绪` - 扩展程序连接成功

## 测试流程

### 基本连接测试
1. 打开调试页面
2. 等待自动检查完成
3. 查看扩展程序状态
4. 如果状态为"已连接"，则基本连接正常

### 通信测试
1. 点击"开始通信测试"
2. 观察日志中的消息流
3. 确认收到各种类型的响应

### 功能测试
1. 点击"检查页面支持"
2. 点击"发送测试消息"
3. 观察响应情况

## 故障排除

### 重新安装扩展程序
1. 在扩展程序页面卸载现有扩展
2. 重新打包扩展程序
3. 重新安装扩展程序
4. 刷新测试页面

### 检查浏览器兼容性
- 确保使用Chrome浏览器
- 确保Chrome版本较新
- 确保没有其他扩展程序冲突

### 网络环境检查
- 确保使用localhost或127.0.0.1
- 确保使用HTTP/HTTPS协议
- 避免使用file://协议

## 获取帮助

如果问题仍然存在，请提供以下信息：
- 浏览器版本和类型
- 扩展程序安装状态截图
- 调试页面的完整日志
- 控制台的错误信息
- 当前访问的URL

---
📞 如需进一步帮助，请查看 EXTENSION_TEST.md 文件或联系开发者。