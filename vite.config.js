import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',     // 允许外部访问
    port: 5173,          // 端口号
    open: true,          // 自动打开浏览器
    strictPort: false,   // 如果端口被占用，尝试下一个可用端口
    cors: true,          // 启用CORS
    https: false,        // 暂时禁用HTTPS，可以改为true启用
    hmr: {
      port: 24678        // HMR端口
    }
  },
  preview: {
    host: '0.0.0.0',     // 预览模式也允许外部访问
    port: 4173,
    https: false         // 预览模式的HTTPS设置
  }
})
