# 扩展程序测试指南

## 🔍 问题诊断

根据分析，扩展程序检测不到的问题主要出现在以下几个方面：

### 1. 扩展程序安装确认
首先确认扩展程序是否正确安装：

```bash
# 检查扩展程序是否已安装
1. 打开 chrome://extensions/
2. 确认"系统音频录制助手"已启用
3. 确认没有错误提示
```

### 2. 扩展程序权限确认
确认扩展程序有正确的权限：

```bash
# 检查权限
1. 在扩展程序页面点击"详情"
2. 确认有以下权限：
   - 标签页捕获
   - 桌面捕获
   - 活动标签页
   - 存储
   - 脚本执行
```

### 3. 测试页面使用

#### 方法1：使用测试页面
1. 打开 `extension_test.html` 文件
2. 查看扩展程序连接状态
3. 观察消息日志中的通信情况

#### 方法2：使用主应用
1. 启动主应用：`npm start`
2. 打开 `http://localhost:3000`
3. 查看浏览器控制台日志
4. 观察扩展程序状态

### 4. 扩展程序工作原理

#### 通信流程：
```
前端网页 ←→ 内容脚本 ←→ 扩展程序后台
```

#### 消息类型：
- `contentScriptReady`: 内容脚本准备就绪
- `checkPageSupport`: 检查页面支持
- `startRecording`: 开始录制
- `stopRecording`: 停止录制
- `recordingStarted`: 录制开始
- `recordingStopped`: 录制停止

### 5. 常见问题解决

#### 问题1：扩展程序未检测到
**症状：** 前端显示"扩展程序未安装或不可用"

**解决方法：**
1. 确认扩展程序已正确安装
2. 刷新页面
3. 检查浏览器控制台是否有错误
4. 确认页面在localhost或HTTPS环境下

#### 问题2：扩展程序已安装但无法通信
**症状：** 扩展程序显示为已安装，但前端无法连接

**解决方法：**
1. 重新加载扩展程序
2. 检查内容脚本是否正确注入
3. 确认manifest.json中的content_scripts配置正确
4. 检查页面URL是否在host_permissions范围内

#### 问题3：音频录制失败
**症状：** 扩展程序已连接，但录制失败

**解决方法：**
1. 确认Chrome版本支持tabCapture API
2. 检查页面是否有音频播放
3. 尝试使用桌面音频源
4. 查看扩展程序后台控制台日志

### 6. 调试步骤

#### 步骤1：检查扩展程序状态
```bash
# 在浏览器控制台执行
console.log('Chrome API:', window.chrome);
console.log('Runtime API:', window.chrome.runtime);
```

#### 步骤2：检查内容脚本
```bash
# 在浏览器控制台执行
window.postMessage({ type: 'checkPageSupport' }, '*');
```

#### 步骤3：检查扩展程序响应
```bash
# 观察控制台输出，应该看到：
# 📨 收到扩展程序消息: contentScriptReady
# 📨 收到扩展程序消息: pageSupport
```

### 7. 扩展程序重新安装

如果问题持续存在，尝试重新安装：

```bash
# 1. 卸载现有扩展程序
# 2. 重新打包
./package_simple.sh

# 3. 重新安装扩展程序
# 4. 刷新测试页面
```

### 8. 扩展程序文件结构

```
extension/
├── manifest.json          # 扩展程序配置
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup/                # 弹出窗口
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
└── icons/                # 图标文件
    ├── icon16.png
    ├── icon32.png
    ├── icon48.png
    └── icon128.png
```

## 🚀 快速测试

1. **安装扩展程序**
   ```bash
   ./package_simple.sh
   # 然后在Chrome中安装生成的CRX文件
   ```

2. **测试连接**
   ```bash
   # 打开测试页面
   open extension_test.html
   ```

3. **查看日志**
   ```bash
   # 在浏览器控制台查看消息
   # 应该看到 "🎉 内容脚本已准备就绪"
   ```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：
- 浏览器版本
- 扩展程序安装状态截图
- 浏览器控制台日志
- 测试页面的具体表现