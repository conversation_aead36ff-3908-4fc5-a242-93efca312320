#!/bin/bash

# =====================================================================
# 系统音频录制助手 - 简化的Chrome扩展程序打包脚本
# 功能：创建ZIP格式的扩展程序包，可直接重命名为.crx
# 使用方法：chmod +x package_simple.sh && ./package_simple.sh
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 简化打包脚本"
echo "==========================================="
echo

# 检查必要文件
echo "检查扩展程序文件..."
REQUIRED_FILES=("manifest.json" "background.js" "content.js")
MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "错误：缺少必要文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    exit 1
fi

echo "✓ 所有必要文件已就绪"

# 获取扩展程序信息
echo "读取扩展程序信息..."
EXTENSION_NAME=$(grep -o '"name": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)
EXTENSION_VERSION=$(grep -o '"version": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)

echo "扩展程序名称：$EXTENSION_NAME"
echo "扩展程序版本：$EXTENSION_VERSION"

# 创建打包目录
PACKAGE_DIR="dist"
if [ -d "$PACKAGE_DIR" ]; then
    echo "清理旧打包目录..."
    rm -rf "$PACKAGE_DIR"
fi

mkdir -p "$PACKAGE_DIR"

# 复制所有文件到打包目录（排除dist目录、脚本文件和已生成的包文件）
echo "复制扩展程序文件..."
rsync -av --exclude='*.sh' --exclude='*.bat' --exclude='*.md' --exclude='.git' --exclude='node_modules' --exclude='dist' --exclude='*.crx' --exclude='*.zip' . "$PACKAGE_DIR/"
cd "$PACKAGE_DIR"

# 创建ZIP文件
echo "创建ZIP文件..."
ZIP_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.zip"
zip -r "$ZIP_FILE" .

# 创建CRX文件（简单重命名）
echo "创建CRX文件..."
CRX_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.crx"
cp "$ZIP_FILE" "$CRX_FILE"

cd ..

echo "==========================================="
echo "打包完成！"
echo "==========================================="
echo
echo "生成的文件："
echo "📦 ZIP文件：$PACKAGE_DIR/$ZIP_FILE"
echo "📦 CRX文件：$PACKAGE_DIR/$CRX_FILE"
echo
echo "安装方法："
echo "方法1（推荐）："
echo "1. 将 $CRX_FILE 拖拽到Chrome扩展程序页面"
echo "2. 确认安装即可"
echo
echo "方法2："
echo "1. 打开Chrome扩展程序页面：chrome://extensions/"
echo "2. 启用'开发者模式'"
echo "3. 点击'加载已解压的扩展程序'"
echo "4. 选择 $PACKAGE_DIR 文件夹"
echo
echo "注意："
echo "- ZIP文件可以直接重命名为.crx文件使用"
echo "- 这种方法生成的CRX文件没有数字签名"
echo "- 适合开发测试和内部分发"
echo

# 询问是否要测试安装
read -p "是否要打开Chrome扩展程序页面进行测试安装？(y/n): " test_install
if [ "$test_install" = "y" ] || [ "$test_install" = "Y" ]; then
    # 检测Chrome浏览器
    CHROME_CMD=""
    if command -v google-chrome &> /dev/null; then
        CHROME_CMD="google-chrome"
    elif command -v google-chrome-stable &> /dev/null; then
        CHROME_CMD="google-chrome-stable"
    elif command -v chromium-browser &> /dev/null; then
        CHROME_CMD="chromium-browser"
    elif command -v chromium &> /dev/null; then
        CHROME_CMD="chromium"
    elif [ -d "/Applications/Google Chrome.app" ]; then
        CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    fi

    if [ -n "$CHROME_CMD" ]; then
        echo "正在打开Chrome扩展程序页面..."
        "$CHROME_CMD" chrome://extensions/ &
    else
        echo "请手动打开Chrome并访问：chrome://extensions/"
    fi
fi

echo "🎉 打包脚本执行完成！"