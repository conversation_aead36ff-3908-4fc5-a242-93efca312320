# 安装脚本使用指南

## 概述

本目录包含多个安装脚本，用于简化Chrome扩展程序的安装过程。由于Chrome的安全限制，完全自动化安装是不可能的，但我们的脚本可以大大简化手动操作。

## 为什么无法完全自动化？

Chrome浏览器出于安全考虑，有以下限制：
1. **安全策略**：不允许脚本自动安装扩展程序
2. **用户确认**：必须用户手动确认安装
3. **开发者模式**：需要用户手动开启
4. **文件夹选择**：需要用户手动选择扩展程序文件夹

## 可用的安装脚本

## 脚本文件

### 安装脚本

#### Windows系统
- **文件名**: `install.bat`
- **使用方法**: 双击运行或在命令行执行 `install.bat`
- **功能**: 自动打开Chrome扩展程序管理页面和文件夹选择器
- **适合用户**: 所有Windows用户

### 打包脚本（推荐用于分发）

#### Windows系统
- **文件名**: `package_and_install.bat`
- **使用方法**: 双击运行或在命令行执行
- **功能**: 一键打包扩展程序并打开Chrome进行安装
- **适合用户**: 需要分发扩展程序的用户

- **文件名**: `package.bat`
- **使用方法**: 双击运行或在命令行执行
- **功能**: 仅打包扩展程序，不自动安装
- **适合用户**: 需要创建安装包的用户

#### Mac/Linux系统
- **文件名**: `package_and_install.sh`
- **使用方法**: `chmod +x package_and_install.sh && ./package_and_install.sh`
- **功能**: 一键打包扩展程序并打开Chrome进行安装
- **适合用户**: 需要分发扩展程序的用户

- **文件名**: `package.sh`
- **使用方法**: `chmod +x package.sh && ./package.sh`
- **功能**: 仅打包扩展程序，不自动安装
- **适合用户**: 需要创建安装包的用户

### Mac/Linux系统（基础版）
- **文件名**: `install.sh`
- **使用方法**: 
  ```bash
  chmod +x install.sh
  ./install.sh
  ```
- **功能**: 自动检测Chrome浏览器并打开扩展程序管理页面
- **适合用户**: 新手用户，需要详细指导

### Mac快速安装版
- **文件名**: `install_quick.sh`
- **使用方法**: 
  ```bash
  chmod +x install_quick.sh
  ./install_quick.sh
  ```
- **功能**: 复制路径到剪贴板，简化文件选择步骤
- **适合用户**: 追求效率的Mac用户

### Mac智能安装版
- **文件名**: `install_smart.sh`
- **使用方法**: 
  ```bash
  chmod +x install_smart.sh
  ./install_smart.sh
  ```
- **功能**: 自动复制路径+详细指导+检查清单
- **适合用户**: 所有用户，最推荐的安装方式

### Mac自动化安装版
- **文件名**: `install_auto.sh`
- **使用方法**: 
  ```bash
  chmod +x install_auto.sh
  ./install_auto.sh
  ```
- **功能**: 提供多种安装选项，包括AppleScript辅助
- **适合用户**: 高级用户，接受半自动化操作

## 使用步骤

### Windows用户
1. 双击运行 `install.bat`
2. 脚本会自动打开Chrome扩展程序页面
3. 按照屏幕提示开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择当前extension文件夹
6. 完成安装

### Mac/Linux用户
1. 打开终端，进入extension目录
2. 执行 `chmod +x install.sh` 添加执行权限
3. 运行 `./install.sh`
4. 脚本会自动检测并打开Chrome浏览器
5. 按照提示完成安装步骤

## 脚本功能

### 自动化功能
- **智能浏览器检测**: 支持多种Chrome安装路径：
  - 系统PATH中的Chrome命令
  - `/Applications/Google Chrome.app/` (Mac标准安装)
  - `$HOME/Applications/Google Chrome.app/` (用户目录安装)
  - Chromium浏览器
- **页面打开**: 自动打开扩展程序管理页面
- **文件夹打开**: 自动打开文件管理器选择扩展程序文件夹
- **错误处理**: 提供详细的错误提示和解决方案
- **路径处理**: 正确处理包含空格的路径名

### 用户指导
- **步骤说明**: 详细的安装步骤指导
- **故障排除**: 常见问题的解决方案
- **完成提示**: 安装完成后的使用指导

## 故障排除

### 常见问题
1. **浏览器未找到**
   - 确保已安装Chrome或Chromium浏览器
   - 检查浏览器是否在系统PATH中
   - 确认Chrome安装在标准位置：
     - Mac: `/Applications/Google Chrome.app/`
     - Linux: `/usr/bin/google-chrome` 或 `/opt/google/chrome/`
     - 用户目录: `$HOME/Applications/Google Chrome.app/`

2. **权限问题**
   - Windows: 以管理员身份运行脚本
   - Mac/Linux: 使用 `chmod +x` 添加执行权限

3. **扩展程序加载失败**
   - 确保已开启开发者模式
   - 检查manifest.json文件格式
   - 确认Chrome版本支持

### 手动安装
如果脚本无法正常运行，可以手动安装：
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择extension文件夹

## 打包安装详细说明

### 为什么推荐打包安装？

打包安装相比开发者模式安装有以下优势：
- **用户体验更好**：拖拽即可安装，无需启用开发者模式
- **更适合分发**：单个.crx文件便于传输和分享
- **更安全**：包含数字签名，确保文件完整性
- **更专业**：适合生产环境使用

### 打包脚本使用方法

#### 一键打包和安装（推荐）

**Windows用户：**
```batch
# 双击运行
package_and_install.bat
```

**Mac/Linux用户：**
```bash
chmod +x package_and_install.sh
./package_and_install.sh
```

**功能特点：**
- 自动生成.crx文件
- 创建数字签名
- 自动打开Chrome扩展程序页面
- 复制文件路径到剪贴板
- 详细的安装指导

#### 仅打包不安装

**Windows用户：**
```batch
# 双击运行
package.bat
```

**Mac/Linux用户：**
```bash
chmod +x package.sh
./package.sh
```

**功能特点：**
- 仅创建.crx文件
- 适合批量打包
- 可用于CI/CD流程

### 打包文件说明

**生成的文件：**
- **.crx文件**：Chrome扩展程序安装包
- **.pem文件**：私钥文件（用于后续版本更新）
- **.zip文件**：备用格式（可重命名为.crx）

**重要提醒：**
- ⚠️ 请妥善保管.pem私钥文件
- ⚠️ 后续更新扩展程序需要使用相同的私钥
- ⚠️ 分发时只需要.crx文件，不需要私钥

### 安装打包的扩展程序

#### 方法一：拖拽安装（最简单）
1. 打开Chrome扩展程序页面：`chrome://extensions/`
2. 将.crx文件拖拽到页面中
3. 确认安装对话框
4. 完成安装

#### 方法二：文件选择器安装
1. 打开Chrome扩展程序页面：`chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择.crx文件
5. 完成安装

### 分发给其他用户

**分发步骤：**
1. 运行打包脚本生成.crx文件
2. 将.crx文件发送给其他用户
3. 用户直接拖拽安装即可

**用户操作：**
- 无需启用开发者模式
- 无需了解技术细节
- 拖拽即可完成安装

## 技术支持

如遇到问题：
1. 查看 `README.md` 文档
2. 检查浏览器控制台错误信息
3. 确保Chrome版本为最新
4. 联系技术支持

---

**注意**: 开发调试建议使用开发者模式安装，生产环境分发建议使用打包的.crx文件。