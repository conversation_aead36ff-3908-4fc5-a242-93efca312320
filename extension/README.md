# 系统音频录制助手 - Chrome扩展程序

## 概述

这是一个Chrome浏览器扩展程序，用于捕获系统音频并传输到网页应用进行录制。通过这个扩展程序，用户可以在网页中录制标签页音频或完整的系统音频。

## 功能特性

- 🎵 **标签页音频捕获** - 录制当前浏览器标签页的音频
- 🖥️ **桌面音频捕获** - 录制完整的系统音频输出
- 🔄 **实时音频传输** - 低延迟的音频数据传输
- 🎛️ **音频设置** - 支持采样率、声道数、比特率配置
- 📱 **用户友好界面** - 简洁直观的操作界面

## 安装说明

### 方法一：使用安装脚本（推荐）

我们提供了三种不同自动化程度的安装脚本：

#### 1. 基础安装脚本 (install.sh / install.bat)
**适用于：所有用户**
```bash
# Mac/Linux
chmod +x install.sh
./install.sh

# Windows (双击运行)
install.bat
```

**功能特点：**
- 自动检测Chrome浏览器
- 打开扩展程序管理页面
- 提供详细的安装步骤指导
- 自动打开文件夹进行选择

#### 2. 快速安装脚本 (install_quick.sh)
**适用于：Mac用户，追求最简流程**
```bash
chmod +x install_quick.sh
./install_quick.sh
```

**功能特点：**
- 一键复制路径到剪贴板
- 自动打开Chrome扩展程序页面
- 简化的安装步骤说明
- 文件完整性检查

#### 3. 智能安装脚本 (install_smart.sh)
**适用于：所有用户，追求便捷安装**
```bash
chmod +x install_smart.sh
./install_smart.sh
```

**功能特点：**
- 自动复制路径到剪贴板
- 一键打开Chrome扩展程序页面
- 详细的步骤指导
- 文件完整性检查
- 安装完成检查清单

#### 4. 自动化安装脚本 (install_auto.sh)
**适用于：高级用户，接受半自动化操作**
```bash
chmod +x install_auto.sh
./install_auto.sh
```

**功能特点：**
- 提供三种安装选项
- AppleScript辅助自动化（可选）
- 详细的安装后验证
- 完整的故障排除指南

### 脚本选择建议

| 用户类型 | 推荐脚本 | 自动化程度 | 复杂度 | 主要特点 |
|---------|---------|-----------|--------|----------|
| 新手用户 | `install.sh` | 低 | 简单 | 基础指导，适合初学者 |
| Mac用户 | `install_quick.sh` | 中 | 简单 | 剪贴板辅助，快速安装 |
| 智能用户 | `install_smart.sh` | 高 | 简单 | 路径复制+详细指导 |
| 高级用户 | `install_auto.sh` | 最高 | 中等 | AppleScript自动化 |
| Windows用户 | `install.bat` | 低 | 简单 | 基础安装，详细指导 |

### 方法二：手动安装

#### 步骤详解
1. **准备文件**
   - 确保extension文件夹包含所有必要文件：
     - `manifest.json` - 扩展程序配置
     - `background.js` - 后台脚本
     - `content.js` - 内容脚本
     - `popup/` - 弹出窗口文件
     - `icons/` - 图标文件

2. **打开扩展程序管理**
   - Chrome地址栏输入：`chrome://extensions/`
   - 或通过菜单：Chrome菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开启开发者模式

4. **加载扩展程序**
   - 点击"加载已解压的扩展程序"按钮
   - 在文件选择器中选择extension文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 扩展程序出现在列表中
   - 浏览器工具栏显示扩展程序图标
   - 可以点击图标测试弹出窗口

### 方法三：打包安装（推荐用于分发）

#### 使用简化打包脚本（推荐）

**简化打包脚本：**
```bash
# Mac/Linux
chmod +x package_simple.sh
./package_simple.sh

# Windows (双击运行)
package_simple.bat
```

**打包脚本功能：**
- ✅ 创建ZIP格式的扩展程序包
- ✅ 生成可直接使用的.crx文件
- ✅ 自动打开Chrome扩展程序页面
- ✅ 避免复杂的数字签名问题
- ✅ 适合开发测试和内部分发

#### 注意：关于CRX_HEADER_INVALID错误

如果遇到"CRX_HEADER_INVALID"错误，请使用以下方法：

**方法1：使用简化打包脚本**
- 运行 `package_simple.sh` 或 `package_simple.bat`
- 生成的CRX文件不会有格式错误

**方法2：直接使用ZIP文件**
- 将ZIP文件重命名为.crx扩展名
- 或者直接在开发者模式下选择整个文件夹

**方法3：手动安装**
- 使用 `install.sh` 或 `install.bat` 脚本
- 直接选择文件夹进行安装

#### 手动打包

1. **创建扩展程序包**
   - 在扩展程序管理页面点击"打包扩展程序"
   - 选择extension文件夹作为根目录
   - 可选：输入私钥文件路径
   - 点击"打包扩展程序"

2. **安装.crx文件**
   - 将生成的.crx文件拖拽到扩展程序页面
   - 确认安装对话框
   - 扩展程序自动安装

3. **分发使用**
   - .crx文件可以分发给其他用户
   - 用户直接拖拽即可安装
   - 适合团队内部使用

#### 打包安装的优势

| 安装方式 | 优点 | 缺点 | 适用场景 |
|---------|------|------|----------|
| 开发者模式 | 无需打包，调试方便 | 需要手动启用开发者模式 | 开发调试 |
| 打包安装 | 一键安装，用户体验好 | 需要打包步骤 | 分发给用户 |
| 应用商店 | 最正式的发布方式 | 需要审核，时间较长 | 公开发布 |

## 安装脚本详细说明

### install.bat (Windows)

**功能特点：**
- 自动检测Chrome浏览器
- 一键打开扩展程序管理页面
- 提供详细的中文安装指导
- 自动打开文件管理器选择文件夹
- 包含错误检查和故障排除建议

**使用方法：**
```batch
# 双击运行或在命令行执行
install.bat
```

**脚本流程：**
1. 显示欢迎信息和功能说明
2. 自动打开 `chrome://extensions/` 页面
3. 显示详细的安装步骤指导
4. 等待用户确认后打开当前文件夹
5. 提供安装完成后的使用指导

### install.sh (Mac/Linux)

**功能特点：**
- 智能检测Chrome/Chromium浏览器
- 支持多种Chrome变种（Chrome、Chromium等）
- 交互式安装指导
- 自动文件夹打开功能
- 完整的环境检查

**使用方法：**
```bash
# 添加执行权限
chmod +x install.sh

# 运行安装脚本
./install.sh
```

**脚本流程：**
1. 检测系统中的Chrome浏览器
2. 显示欢迎信息和检测结果
3. 自动打开扩展程序管理页面
4. 提供详细的安装步骤说明
5. 交互式指导用户完成安装
6. 安装完成后提供使用指导

### 脚本错误处理

**常见问题解决：**
- **浏览器未找到**：提示手动打开 `chrome://extensions/`
- **权限问题**：提示使用 `chmod +x` 添加执行权限
- **文件夹问题**：确保在正确的extension目录中运行脚本
- **Chrome版本问题**：提示更新Chrome到最新版本

**故障排除：**
1. 检查Chrome是否为最新版本
2. 确认extension文件夹包含完整文件
3. 验证开发者模式是否已开启
4. 检查文件权限和路径是否正确

## 使用说明

### 1. 扩展程序弹出窗口

点击浏览器工具栏中的扩展程序图标，打开弹出窗口：

- **页面状态** - 显示当前页面的支持状态
- **录制控制** - 开始/停止录制按钮
- **音频源选择** - 选择标签页音频或桌面音频
- **音频设置** - 配置采样率、声道数、比特率

### 2. 网页应用集成

扩展程序与网页应用通过消息传递机制通信：

1. **打开音频录制网页**
   - 确保网页在支持的域名下（localhost或HTTPS）

2. **扩展程序自动检测**
   - 网页会自动检测扩展程序是否已安装
   - 显示扩展程序连接状态

3. **开始录制**
   - 选择音频源类型
   - 点击开始录制按钮
   - 授权音频捕获权限

4. **录制完成**
   - 点击停止录制
   - 音频文件会自动保存到网页应用中

## 技术架构

### 扩展程序结构

```
extension/
├── manifest.json          # 扩展程序配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup/
│   ├── popup.html         # 弹出窗口界面
│   ├── popup.js           # 弹出窗口逻辑
│   └── popup.css          # 弹出窗口样式
└── icons/                # 图标文件
```

### 核心API

- **chrome.tabCapture** - 捕获标签页音频
- **chrome.desktopCapture** - 捕获桌面音频
- **chrome.runtime** - 扩展程序间通信
- **Web Audio API** - 音频数据处理
- **MediaRecorder API** - 音频录制

### 通信机制

```
网页应用 ←→ 内容脚本 ←→ 后台脚本 ←→ Chrome API
```

## 配置选项

### 音频设置

- **采样率**：44.1kHz, 48kHz, 96kHz
- **声道数**：单声道, 立体声
- **比特率**：128kbps, 192kbps, 256kbps, 320kbps

### 权限要求

扩展程序需要以下权限：
- `tabCapture` - 标签页音频捕获
- `desktopCapture` - 桌面音频捕获
- `activeTab` - 当前标签页访问
- `storage` - 本地存储
- `scripting` - 脚本注入

## 故障排除

### 常见问题

1. **扩展程序无法加载**
   - 确保已启用开发者模式
   - 检查manifest.json文件格式
   - 确认文件夹路径正确

2. **音频捕获失败**
   - 检查浏览器是否为最新版本
   - 确认有音频播放内容
   - 检查系统音频设置

3. **网页无法检测扩展程序**
   - 确保网页在支持的域名下
   - 检查content_scripts配置
   - 刷新网页页面

4. **权限被拒绝**
   - 在扩展程序管理页面检查权限设置
   - 重新安装扩展程序
   - 检查Chrome安全设置

### 调试方法

1. **查看控制台日志**
   - 扩展程序页面点击"服务工作线程"查看后台脚本日志
   - 网页开发者工具查看内容脚本日志

2. **检查扩展程序状态**
   - 确保扩展程序已启用
   - 检查是否有错误信息

3. **测试音频捕获**
   - 先使用扩展程序弹出窗口测试
   - 确认音频源选择正确

## 安全注意事项

### 隐私保护

- 扩展程序仅在用户主动启动时捕获音频
- 音频数据仅传输到当前打开的网页
- 不会存储或传输用户隐私信息

### 权限管理

- 仅请求必要的权限
- 定期检查权限使用情况
- 在不需要时可以禁用扩展程序

### 安全建议

- 仅从可信来源安装扩展程序
- 定期更新扩展程序
- 注意检查权限请求

## 开发指南

### 自定义扩展程序

1. **修改manifest.json**
   - 更新扩展程序名称和版本
   - 调整权限配置
   - 修改内容脚本匹配规则

2. **自定义音频处理**
   - 修改background.js中的音频处理逻辑
   - 调整音频参数配置
   - 添加错误处理机制

3. **界面定制**
   - 修改popup.html和popup.css
   - 更新图标文件
   - 调整弹出窗口大小

### 构建和发布

1. **测试扩展程序**
   - 在开发者模式下充分测试
   - 验证各种音频源和设置
   - 测试错误处理机制

2. **准备发布**
   - 创建扩展程序包
   - 准备应用商店描述
   - 制作宣传截图

3. **发布到应用商店**
   - 注册Chrome开发者账号
   - 提交扩展程序审核
   - 发布到Chrome应用商店

## 技术支持

如果您在使用过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 确保扩展程序版本为最新
4. 联系技术支持

## 版本历史

### v1.0.0
- 初始版本发布
- 支持标签页和桌面音频捕获
- 基本的音频设置配置
- 网页应用集成功能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**注意**：本扩展程序仅用于合法的音频录制目的，请遵守相关法律法规和隐私保护要求。