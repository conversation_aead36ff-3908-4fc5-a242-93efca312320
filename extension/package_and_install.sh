#!/bin/bash

# =====================================================================
# 系统音频录制助手 - 一键打包和安装脚本
# 功能：自动打包扩展程序并打开Chrome进行安装
# 使用方法：chmod +x package_and_install.sh && ./package_and_install.sh
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 一键打包和安装"
echo "==========================================="
echo

# 检查必要文件
echo "检查扩展程序文件..."
REQUIRED_FILES=("manifest.json" "background.js" "content.js")
MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "错误：缺少必要文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    exit 1
fi

echo "✓ 所有必要文件已就绪"

# 获取扩展程序信息
echo "读取扩展程序信息..."
EXTENSION_NAME=$(grep -o '"name": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)
EXTENSION_VERSION=$(grep -o '"version": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)

echo "扩展程序名称：$EXTENSION_NAME"
echo "扩展程序版本：$EXTENSION_VERSION"

# 创建临时打包目录
TEMP_DIR="/tmp/extension_package_$$"
mkdir -p "$TEMP_DIR"

# 复制所有文件到临时目录
echo "复制扩展程序文件..."
cp -r . "$TEMP_DIR/"
cd "$TEMP_DIR"

# 删除不需要的文件
echo "清理不需要的文件..."
rm -f *.sh *.bat *.md
rm -rf .git 2>/dev/null
rm -rf node_modules 2>/dev/null

# 创建ZIP文件
echo "创建ZIP文件..."
ZIP_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.zip"
zip -r "$ZIP_FILE" . -x "*.sh" "*.bat" "*.md" ".git/*" "node_modules/*"

# 生成私钥（如果不存在）
PRIVATE_KEY="../extension_key.pem"
if [ ! -f "$PRIVATE_KEY" ]; then
    echo "生成私钥文件..."
    openssl genrsa -out "$PRIVATE_KEY" 2048
    echo "✓ 私钥已生成：$PRIVATE_KEY"
    echo "⚠️  请妥善保管此私钥文件，用于后续版本更新"
else
    echo "使用现有私钥：$PRIVATE_KEY"
fi

# 创建CRX文件
echo "创建CRX文件..."
CRX_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.crx"

# CRX文件格式：魔数 + 版本 + 公钥长度 + 签名长度 + 公钥 + 签名 + ZIP内容
echo "Cr24" > "$CRX_FILE"
echo -n -e "\x02\x00\x00\x00" >> "$CRX_FILE"

# 获取公钥
PUBLIC_KEY_DER=$(openssl rsa -in "$PRIVATE_KEY" -pubout -outform DER)
PUBLIC_KEY_LENGTH=${#PUBLIC_KEY_DER}

# 计算签名
SIGNATURE=$(openssl dgst -sha256 -binary "$ZIP_FILE" | openssl dgst -sha256 -sign "$PRIVATE_KEY" | openssl enc -base64)

# 写入公钥长度和签名长度
printf "%08x" $PUBLIC_KEY_LENGTH | xxd -r -p >> "$CRX_FILE"
printf "%08x" ${#SIGNATURE} | xxd -r -p >> "$CRX_FILE"

# 写入公钥和签名
echo "$PUBLIC_KEY_DER" >> "$CRX_FILE"
echo "$SIGNATURE" | openssl enc -base64 -d >> "$CRX_FILE"

# 写入ZIP内容
cat "$ZIP_FILE" >> "$CRX_FILE"

# 复制CRX文件到原目录
cp "$CRX_FILE" "$OLDPWD/"

cd "$OLDPWD"
rm -rf "$TEMP_DIR"

echo "==========================================="
echo "打包完成！"
echo "==========================================="
echo
echo "生成的文件："
echo "📦 CRX文件：$CRX_FILE"
echo "🔑 私钥文件：$PRIVATE_KEY"
echo
echo "正在准备安装..."

# 检测Chrome浏览器
CHROME_CMD=""
if command -v google-chrome &> /dev/null; then
    CHROME_CMD="google-chrome"
elif command -v google-chrome-stable &> /dev/null; then
    CHROME_CMD="google-chrome-stable"
elif command -v chromium-browser &> /dev/null; then
    CHROME_CMD="chromium-browser"
elif command -v chromium &> /dev/null; then
    CHROME_CMD="chromium"
elif [ -d "/Applications/Google Chrome.app" ]; then
    CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

if [ -z "$CHROME_CMD" ]; then
    echo "错误：未找到Chrome浏览器"
    echo "请手动打开Chrome并访问：chrome://extensions/"
    echo "然后将 $CRX_FILE 拖拽到扩展程序页面"
    exit 1
fi

echo "发现浏览器：$CHROME_CMD"

# 复制CRX文件路径到剪贴板
echo "$OLDPWD/$CRX_FILE" | pbcopy

echo
echo "==========================================="
echo "安装步骤"
echo "==========================================="
echo

echo "1. 🔄 正在打开Chrome扩展程序页面..."
"$CHROME_CMD" chrome://extensions/ &
sleep 2

echo "2. 🔧 在Chrome中："
echo "   - 启用'开发者模式'（如果尚未启用）"
echo "   - 将CRX文件拖拽到扩展程序页面"
echo

echo "3. 📦 文件位置："
echo "   - CRX文件路径：$OLDPWD/$CRX_FILE"
echo "   - 路径已复制到剪贴板（可使用Cmd+V粘贴）"
echo

echo "4. ✅ 安装验证："
echo "   - 扩展程序应该出现在列表中"
echo "   - 状态显示为'已启用'"
echo "   - 工具栏显示扩展程序图标"
echo

echo "==========================================="
echo "安装提示"
echo "==========================================="
echo

echo "💡 如果拖拽安装失败："
echo "   - 确保'开发者模式'已启用"
echo "   - 尝试点击'加载已解压的扩展程序'选择CRX文件"
echo

echo "💡 如果提示错误："
echo "   - 检查CRX文件是否完整"
echo "   - 确保Chrome版本支持"
echo "   - 尝试重新打包"
echo

echo "💡 分发说明："
echo "   - 将 $CRX文件 分发给其他用户"
echo "   - 用户可以直接拖拽安装"
echo "   - 无需启用开发者模式"
echo

echo "==========================================="
echo "打包和安装准备完成！"
echo "==========================================="
echo "请按照上述步骤完成Chrome扩展程序的安装。"
echo "CRX文件路径已复制到剪贴板，方便拖拽安装。"