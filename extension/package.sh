#!/bin/bash

# =====================================================================
# 系统音频录制助手 - Chrome扩展程序打包脚本
# 功能：将扩展程序打包为.crx文件，便于分发和安装
# 使用方法：chmod +x package.sh && ./package.sh
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 扩展程序打包脚本"
echo "==========================================="
echo

# 检查必要文件
echo "检查扩展程序文件..."
REQUIRED_FILES=("manifest.json" "background.js" "content.js")
MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "错误：缺少必要文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    exit 1
fi

echo "✓ 所有必要文件已就绪"

# 获取扩展程序信息
echo "读取扩展程序信息..."
EXTENSION_NAME=$(grep -o '"name": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)
EXTENSION_VERSION=$(grep -o '"version": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)

echo "扩展程序名称：$EXTENSION_NAME"
echo "扩展程序版本：$EXTENSION_VERSION"

# 创建打包目录
PACKAGE_DIR="package"
if [ -d "$PACKAGE_DIR" ]; then
    echo "清理旧打包目录..."
    rm -rf "$PACKAGE_DIR"
fi

mkdir -p "$PACKAGE_DIR"

# 复制所有文件到打包目录
echo "复制扩展程序文件..."
cp -r . "$PACKAGE_DIR/"
cd "$PACKAGE_DIR"

# 删除不需要的文件
echo "清理不需要的文件..."
rm -f *.sh *.bat *.md
rm -rf .git 2>/dev/null
rm -rf node_modules 2>/dev/null

# 生成私钥（如果不存在）
PRIVATE_KEY="../extension_key.pem"
if [ ! -f "$PRIVATE_KEY" ]; then
    echo "生成私钥文件..."
    openssl genrsa -out "$PRIVATE_KEY" 2048
    echo "✓ 私钥已生成：$PRIVATE_KEY"
    echo "⚠️  请妥善保管此私钥文件，用于后续版本更新"
else
    echo "使用现有私钥：$PRIVATE_KEY"
fi

# 创建ZIP文件
echo "创建ZIP文件..."
ZIP_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.zip"
zip -r "$ZIP_FILE" . -x "*.sh" "*.bat" "*.md" ".git/*" "node_modules/*"

# 计算文件签名
echo "计算文件签名..."
SIGNATURE=$(openssl dgst -sha256 -binary "$ZIP_FILE" | openssl dgst -sha256 -sign "$PRIVATE_KEY" | openssl enc -base64)

# 创建CRX文件
echo "创建CRX文件..."
CRX_FILE="${EXTENSION_NAME}_${EXTENSION_VERSION}.crx"

# CRX文件格式：魔数 + 版本 + 公钥长度 + 签名长度 + 公钥 + 签名 + ZIP内容
echo "Cr24" > "$CRX_FILE"
echo -n -e "\x02\x00\x00\x00" >> "$CRX_FILE"

# 获取公钥
PUBLIC_KEY=$(openssl rsa -in "$PRIVATE_KEY" -pubout -outform DER | openssl enc -base64 | tr -d '\n')
PUBLIC_KEY_DER=$(openssl rsa -in "$PRIVATE_KEY" -pubout -outform DER)
PUBLIC_KEY_LENGTH=${#PUBLIC_KEY_DER}

# 写入公钥长度和签名长度
printf "%08x" $PUBLIC_KEY_LENGTH | xxd -r -p >> "$CRX_FILE"
printf "%08x" ${#SIGNATURE} | xxd -r -p >> "$CRX_FILE"

# 写入公钥和签名
echo "$PUBLIC_KEY_DER" >> "$CRX_FILE"
echo "$SIGNATURE" | openssl enc -base64 -d >> "$CRX_FILE"

# 写入ZIP内容
cat "$ZIP_FILE" >> "$CRX_FILE"

cd ..

echo "==========================================="
echo "打包完成！"
echo "==========================================="
echo
echo "生成的文件："
echo "📦 CRX文件：$PACKAGE_DIR/$CRX_FILE"
echo "📦 ZIP文件：$PACKAGE_DIR/$ZIP_FILE"
echo "🔑 私钥文件：$PRIVATE_KEY"
echo
echo "安装方法："
echo "1. 将 $CRX_FILE 拖拽到Chrome扩展程序页面"
echo "2. 或者在扩展程序页面点击'加载已解压的扩展程序'选择此文件"
echo
echo "⚠️  重要提醒："
echo "- 请妥善保管 $PRIVATE_KEY 文件"
echo "- 后续更新扩展程序需要使用相同的私钥"
echo "- 分发时只需要.crx文件，不需要私钥"
echo

# 询问是否要测试安装
read -p "是否要打开Chrome扩展程序页面进行测试安装？(y/n): " test_install
if [ "$test_install" = "y" ] || [ "$test_install" = "Y" ]; then
    # 检测Chrome浏览器
    CHROME_CMD=""
    if command -v google-chrome &> /dev/null; then
        CHROME_CMD="google-chrome"
    elif command -v google-chrome-stable &> /dev/null; then
        CHROME_CMD="google-chrome-stable"
    elif command -v chromium-browser &> /dev/null; then
        CHROME_CMD="chromium-browser"
    elif command -v chromium &> /dev/null; then
        CHROME_CMD="chromium"
    elif [ -d "/Applications/Google Chrome.app" ]; then
        CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    fi

    if [ -n "$CHROME_CMD" ]; then
        echo "正在打开Chrome扩展程序页面..."
        "$CHROME_CMD" chrome://extensions/ &
    else
        echo "请手动打开Chrome并访问：chrome://extensions/"
    fi
fi

echo "🎉 打包脚本执行完成！"