#!/bin/bash

# =====================================================================
# 系统音频录制助手 - Chrome扩展程序安装脚本
# 适用于：Mac OS X 和 Linux 系统
# 功能：自动检测Chrome浏览器并打开扩展程序管理页面
# 使用方法：chmod +x install.sh && ./install.sh
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 扩展程序安装脚本"
echo "==========================================="
echo

# 检测Chrome浏览器
echo "正在检测Chrome浏览器..."
CHROME_CMD=""

# 首先检查PATH中的Chrome
if command -v google-chrome &> /dev/null; then
    CHROME_CMD="google-chrome"
elif command -v google-chrome-stable &> /dev/null; then
    CHROME_CMD="google-chrome-stable"
elif command -v chromium-browser &> /dev/null; then
    CHROME_CMD="chromium-browser"
elif command -v chromium &> /dev/null; then
    CHROME_CMD="chromium"
fi

# 如果PATH中没有找到，检查Mac应用程序目录
if [ -z "$CHROME_CMD" ] && [ -d "/Applications/Google Chrome.app" ]; then
    CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

# 检查Chromium
if [ -z "$CHROME_CMD" ] && [ -d "/Applications/Chromium.app" ]; then
    CHROME_CMD="/Applications/Chromium.app/Contents/MacOS/Chromium"
fi

# 检查用户目录中的Chrome
if [ -z "$CHROME_CMD" ] && [ -d "$HOME/Applications/Google Chrome.app" ]; then
    CHROME_CMD="$HOME/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

if [ -z "$CHROME_CMD" ]; then
    echo "错误：未找到Chrome或Chromium浏览器"
    echo "请手动打开Chrome浏览器并访问：chrome://extensions/"
    echo "或者安装Chrome浏览器后重新运行此脚本"
    echo ""
    echo "如果您已安装Chrome，请确保："
    echo "1. Chrome安装在 /Applications/ 目录下"
    echo "2. 或者将Chrome添加到系统PATH中"
    exit 1
fi

echo "发现浏览器：$CHROME_CMD"
echo "正在打开扩展程序管理页面..."
"$CHROME_CMD" chrome://extensions/ &

echo
echo "==========================================="
echo "安装步骤指南"
echo "==========================================="
echo
echo "请按照以下步骤安装扩展程序："
echo
echo "1. 启用开发者模式"
echo "   - 在Chrome扩展程序页面右上角"
echo "   - 打开'开发者模式'开关"
echo
echo "2. 加载扩展程序"
echo "   - 点击'加载已解压的扩展程序'按钮"
echo "   - 选择当前文件夹（包含manifest.json的文件夹）"
echo
echo "3. 验证安装"
echo "   - 扩展程序应该出现在列表中"
echo "   - 浏览器工具栏会显示扩展程序图标"
echo
echo "==========================================="
echo "故障排除"
echo "==========================================="
echo "如果安装失败，请检查："
echo "- 是否已启用开发者模式"
echo "- 文件夹中是否包含manifest.json文件"
echo "- Chrome浏览器是否为最新版本"
echo "- 是否有足够的权限访问文件"
echo

# 等待用户确认后打开文件夹
read -p "按回车键打开扩展程序文件夹..."
echo "打开文件夹：$(pwd)"

# 根据系统类型打开文件夹
if command -v xdg-open &> /dev/null; then
    # Linux系统
    xdg-open .
elif command -v open &> /dev/null; then
    # Mac OS X系统
    open .
else
    echo "请手动打开文件夹：$(pwd)"
fi

echo
echo "==========================================="
echo "安装完成"
echo "==========================================="
echo "安装完成后，您可以："
echo "- 点击浏览器工具栏中的扩展程序图标"
echo "- 在支持的网页中使用音频录制功能"
echo "- 通过弹出窗口配置音频设置"
echo "- 查看网页中的系统音频录制组件"
echo

# 等待用户确认后退出
read -p "按回车键退出..."
echo "安装脚本完成，感谢使用！"
echo "如有问题请查看README.md文档或联系技术支持"