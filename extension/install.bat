@echo off
REM =====================================================================
REM 系统音频录制助手 - Chrome扩展程序安装脚本
REM 适用于：Windows 系统
REM 功能：自动打开Chrome扩展程序管理页面和文件夹
REM 使用方法：双击运行 install.bat
REM =====================================================================

echo ===========================================
echo 系统音频录制助手 - 扩展程序安装脚本
echo ===========================================
echo.

echo 正在打开Chrome扩展程序管理页面...
start chrome://extensions/

echo.
echo ===========================================
echo 安装步骤指南
echo ===========================================
echo.
echo 请按照以下步骤安装扩展程序：
echo.
echo 1. 启用开发者模式
echo    - 在Chrome扩展程序页面右上角
echo    - 打开"开发者模式"开关
echo.
echo 2. 加载扩展程序
echo    - 点击"加载已解压的扩展程序"按钮
echo    - 选择当前文件夹（包含manifest.json的文件夹）
echo.
echo 3. 验证安装
echo    - 扩展程序应该出现在列表中
echo    - 浏览器工具栏会显示扩展程序图标
echo.
echo ===========================================
echo 故障排除
echo ===========================================
echo 如果安装失败，请检查：
echo - 是否已启用开发者模式
echo - 文件夹中是否包含manifest.json文件
echo - Chrome浏览器是否为最新版本
echo - 是否有足够的权限访问文件
echo.

echo 按任意键打开扩展程序文件夹...
pause > nul
explorer .

echo.
echo ===========================================
echo 安装完成
echo ===========================================
echo 安装完成后，您可以：
echo - 点击浏览器工具栏中的扩展程序图标
echo - 在支持的网页中使用音频录制功能
echo - 通过弹出窗口配置音频设置
echo - 查看网页中的系统音频录制组件
echo.

echo 按任意键退出...
pause > nul
echo 安装脚本完成，感谢使用！
echo 如有问题请查看README.md文档或联系技术支持