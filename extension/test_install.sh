#!/bin/bash

echo "=========================================="
echo "测试Chrome扩展程序安装"
echo "=========================================="
echo

# 检查CRX文件是否存在
CRX_FILE="dist/系统音频录制助手_1.0.0.crx"
if [ ! -f "$CRX_FILE" ]; then
    echo "❌ CRX文件不存在，请先运行打包脚本"
    exit 1
fi

echo "✅ CRX文件存在: $CRX_FILE"
echo "✅ 文件大小: $(ls -lh "$CRX_FILE" | awk '{print $5}')"
echo "✅ 文件类型: $(file "$CRX_FILE")"
echo

# 检查CRX文件内容
echo "检查CRX文件内容..."
if unzip -t "$CRX_FILE" > /dev/null 2>&1; then
    echo "✅ CRX文件格式正确，可以正常解压"
else
    echo "❌ CRX文件格式错误"
    exit 1
fi

echo
echo "CRX文件包含的文件："
unzip -l "$CRX_FILE" | grep -E "manifest\.json|background\.js|content\.js" && echo "✅ 所有必要文件都存在" || echo "❌ 缺少必要文件"

echo
echo "=========================================="
echo "安装测试建议："
echo "=========================================="
echo "1. 打开Chrome扩展程序页面：chrome://extensions/"
echo "2. 启用'开发者模式'"
echo "3. 将 $CRX_FILE 拖拽到页面中"
echo "4. 如果出现CRX_HEADER_INVALID错误，请尝试："
echo "   - 方法1：直接选择dist文件夹安装"
echo "   - 方法2：重命名ZIP文件为.crx"
echo "   - 方法3：使用Chrome的'打包扩展程序'功能"
echo

# 询问是否要打开Chrome
read -p "是否要打开Chrome扩展程序页面进行测试？(y/n): " open_chrome
if [ "$open_chrome" = "y" ] || [ "$open_chrome" = "Y" ]; then
    # 检测Chrome浏览器
    CHROME_CMD=""
    if command -v google-chrome &> /dev/null; then
        CHROME_CMD="google-chrome"
    elif command -v google-chrome-stable &> /dev/null; then
        CHROME_CMD="google-chrome-stable"
    elif command -v chromium-browser &> /dev/null; then
        CHROME_CMD="chromium-browser"
    elif command -v chromium &> /dev/null; then
        CHROME_CMD="chromium"
    elif [ -d "/Applications/Google Chrome.app" ]; then
        CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    fi

    if [ -n "$CHROME_CMD" ]; then
        echo "正在打开Chrome扩展程序页面..."
        "$CHROME_CMD" chrome://extensions/ &
    else
        echo "请手动打开Chrome并访问：chrome://extensions/"
    fi
fi

echo "🎉 测试脚本执行完成！"