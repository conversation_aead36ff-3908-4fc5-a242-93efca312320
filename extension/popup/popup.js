/**
 * 弹出窗口JavaScript
 * 处理用户界面交互和扩展程序控制
 */

// 全局状态
let currentState = {
    isRecording: false,
    recordingStartTime: null,
    currentTab: null,
    pageSupport: null,
    selectedSource: 'tab',
    recordingTimer: null
};

// DOM元素
const elements = {
    // 状态元素
    pageTitle: document.getElementById('pageTitle'),
    supportStatus: document.getElementById('supportStatus'),
    
    // 控制按钮
    startTabRecording: document.getElementById('startTabRecording'),
    startDesktopRecording: document.getElementById('startDesktopRecording'),
    stopRecording: document.getElementById('stopRecording'),
    
    // 录制状态
    recordingStatus: document.getElementById('recordingStatus'),
    recordingTime: document.getElementById('recordingTime'),
    
    // 音频源选择
    audioSourceInputs: document.querySelectorAll('input[name="audioSource"]'),
    
    // 设置
    sampleRate: document.getElementById('sampleRate'),
    channelCount: document.getElementById('channelCount'),
    bitrate: document.getElementById('bitrate'),
    
    // 错误信息
    errorSection: document.getElementById('errorSection'),
    errorMessage: document.getElementById('errorMessage'),
    
    // 链接
    helpLink: document.getElementById('helpLink'),
    settingsLink: document.getElementById('settingsLink'),
    aboutLink: document.getElementById('aboutLink')
};

/**
 * 初始化弹出窗口
 */
async function initPopup() {
    console.log('初始化弹出窗口');
    
    try {
        // 获取当前标签页信息
        await getCurrentTabInfo();
        
        // 检查页面支持
        await checkPageSupport();
        
        // 绑定事件监听器
        bindEventListeners();
        
        // 获取录制状态
        await getRecordingStatus();
        
        // 加载设置
        loadSettings();
        
        console.log('弹出窗口初始化完成');
    } catch (error) {
        console.error('初始化失败:', error);
        showError('初始化失败: ' + error.message);
    }
}

/**
 * 获取当前标签页信息
 */
async function getCurrentTabInfo() {
    try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length > 0) {
            currentState.currentTab = tabs[0];
            elements.pageTitle.textContent = tabs[0].title || '未知页面';
            console.log('当前标签页:', tabs[0]);
        }
    } catch (error) {
        console.error('获取标签页信息失败:', error);
        elements.pageTitle.textContent = '获取失败';
    }
}

/**
 * 检查页面支持
 */
async function checkPageSupport() {
    try {
        const response = await sendMessageToBackground({ type: 'checkPageSupport' });
        currentState.pageSupport = response;
        
        if (response.supported) {
            elements.supportStatus.textContent = '✅ 支持';
            elements.supportStatus.style.color = '#38a169';
            enableControls();
        } else {
            elements.supportStatus.textContent = '❌ 不支持';
            elements.supportStatus.style.color = '#e53e3e';
            disableControls();
            
            if (response.error) {
                showError('页面不支持: ' + response.error);
            }
        }
    } catch (error) {
        console.error('检查页面支持失败:', error);
        elements.supportStatus.textContent = '❌ 检查失败';
        elements.supportStatus.style.color = '#e53e3e';
        disableControls();
    }
}

/**
 * 获取录制状态
 */
async function getRecordingStatus() {
    try {
        const response = await sendMessageToBackground({ type: 'getRecordingStatus' });
        if (response.isRecording) {
            currentState.isRecording = true;
            currentState.recordingStartTime = Date.now() - (response.duration || 0);
            showRecordingStatus();
        }
    } catch (error) {
        console.error('获取录制状态失败:', error);
    }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 录制控制按钮
    elements.startTabRecording.addEventListener('click', () => startRecording('tab'));
    elements.startDesktopRecording.addEventListener('click', () => startRecording('desktop'));
    elements.stopRecording.addEventListener('click', stopRecording);
    
    // 音频源选择
    elements.audioSourceInputs.forEach(input => {
        input.addEventListener('change', (e) => {
            currentState.selectedSource = e.target.value;
        });
    });
    
    // 设置变更
    elements.sampleRate.addEventListener('change', saveSettings);
    elements.channelCount.addEventListener('change', saveSettings);
    elements.bitrate.addEventListener('change', saveSettings);
    
    // 链接点击
    elements.helpLink.addEventListener('click', (e) => {
        e.preventDefault();
        showHelp();
    });
    
    elements.settingsLink.addEventListener('click', (e) => {
        e.preventDefault();
        showSettings();
    });
    
    elements.aboutLink.addEventListener('click', (e) => {
        e.preventDefault();
        showAbout();
    });
}

/**
 * 开始录制
 */
async function startRecording(sourceType) {
    try {
        console.log('开始录制:', sourceType);
        
        // 检查页面支持
        if (!currentState.pageSupport || !currentState.pageSupport.supported) {
            showError('当前页面不支持音频录制');
            return;
        }
        
        // 获取音频设置
        const audioConfig = getAudioConfig();
        
        // 发送开始录制消息
        const response = await sendMessageToBackground({
            type: 'startRecording',
            sourceType: sourceType,
            config: audioConfig
        });
        
        if (response.success) {
            currentState.isRecording = true;
            currentState.recordingStartTime = Date.now();
            currentState.selectedSource = sourceType;
            
            showRecordingStatus();
            updateControlButtons();
            
            console.log('录制已开始');
        } else {
            showError('开始录制失败: ' + response.error);
        }
    } catch (error) {
        console.error('开始录制失败:', error);
        showError('开始录制失败: ' + error.message);
    }
}

/**
 * 停止录制
 */
async function stopRecording() {
    try {
        console.log('停止录制');
        
        const response = await sendMessageToBackground({ type: 'stopRecording' });
        
        if (response.success) {
            currentState.isRecording = false;
            currentState.recordingStartTime = null;
            
            hideRecordingStatus();
            updateControlButtons();
            
            console.log('录制已停止');
        } else {
            showError('停止录制失败: ' + response.error);
        }
    } catch (error) {
        console.error('停止录制失败:', error);
        showError('停止录制失败: ' + error.message);
    }
}

/**
 * 显示录制状态
 */
function showRecordingStatus() {
    elements.recordingStatus.style.display = 'block';
    updateControlButtons();
    
    // 开始计时器
    if (currentState.recordingTimer) {
        clearInterval(currentState.recordingTimer);
    }
    
    currentState.recordingTimer = setInterval(updateRecordingTime, 1000);
    updateRecordingTime();
}

/**
 * 隐藏录制状态
 */
function hideRecordingStatus() {
    elements.recordingStatus.style.display = 'none';
    updateControlButtons();
    
    // 停止计时器
    if (currentState.recordingTimer) {
        clearInterval(currentState.recordingTimer);
        currentState.recordingTimer = null;
    }
}

/**
 * 更新录制时间
 */
function updateRecordingTime() {
    if (!currentState.recordingStartTime) return;
    
    const elapsed = Date.now() - currentState.recordingStartTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    
    elements.recordingTime.textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * 更新控制按钮状态
 */
function updateControlButtons() {
    if (currentState.isRecording) {
        elements.startTabRecording.disabled = true;
        elements.startDesktopRecording.disabled = true;
        elements.stopRecording.disabled = false;
    } else {
        elements.startTabRecording.disabled = !currentState.pageSupport?.supported;
        elements.startDesktopRecording.disabled = !currentState.pageSupport?.supported;
        elements.stopRecording.disabled = true;
    }
}

/**
 * 启用控制按钮
 */
function enableControls() {
    elements.startTabRecording.disabled = false;
    elements.startDesktopRecording.disabled = false;
}

/**
 * 禁用控制按钮
 */
function disableControls() {
    elements.startTabRecording.disabled = true;
    elements.startDesktopRecording.disabled = true;
    elements.stopRecording.disabled = true;
}

/**
 * 获取音频配置
 */
function getAudioConfig() {
    return {
        sampleRate: parseInt(elements.sampleRate.value),
        channelCount: parseInt(elements.channelCount.value),
        bitsPerSecond: parseInt(elements.bitrate.value)
    };
}

/**
 * 保存设置
 */
function saveSettings() {
    const settings = getAudioConfig();
    chrome.storage.local.set({ audioSettings: settings }, () => {
        console.log('设置已保存:', settings);
    });
}

/**
 * 加载设置
 */
function loadSettings() {
    chrome.storage.local.get(['audioSettings'], (result) => {
        if (result.audioSettings) {
            const settings = result.audioSettings;
            elements.sampleRate.value = settings.sampleRate || 48000;
            elements.channelCount.value = settings.channelCount || 2;
            elements.bitrate.value = settings.bitsPerSecond || 320000;
        }
    });
}

/**
 * 显示错误信息
 */
function showError(message) {
    elements.errorMessage.textContent = message;
    elements.errorSection.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        elements.errorSection.style.display = 'none';
    }, 3000);
}

/**
 * 隐藏错误信息
 */
function hideError() {
    elements.errorSection.style.display = 'none';
}

/**
 * 显示帮助
 */
function showHelp() {
    alert('系统音频录制助手使用帮助：\n\n' +
          '1. 确保在支持的页面上使用（localhost或HTTPS）\n' +
          '2. 选择音频源类型（标签页或桌面）\n' +
          '3. 点击开始录制按钮\n' +
          '4. 录制完成后点击停止按钮\n\n' +
          '如需更多帮助，请查看扩展程序说明。');
}

/**
 * 显示设置
 */
function showSettings() {
    alert('音频设置已保存在弹出窗口中，包括：\n\n' +
          '• 采样率：音频质量的基础\n' +
          '• 声道数：单声道或立体声\n' +
          '• 比特率：音频压缩质量\n\n' +
          '建议使用默认设置以获得最佳效果。');
}

/**
 * 显示关于
 */
function showAbout() {
    alert('系统音频录制助手 v1.0.0\n\n' +
          '一个Chrome扩展程序，用于捕获系统音频并传输到网页应用进行录制。\n\n' +
          '特性：\n' +
          '• 标签页音频捕获\n' +
          '• 桌面音频捕获\n' +
          '• 实时音频传输\n' +
          '• 多种音频格式支持\n\n' +
          '© 2024 系统音频录制助手');
}

/**
 * 发送消息到后台脚本
 */
function sendMessageToBackground(message) {
    return new Promise((resolve, reject) => {
        try {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 监听来自后台脚本的消息
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('弹出窗口收到消息:', request);
    
    try {
        switch (request.type) {
            case 'recordingStarted':
                currentState.isRecording = true;
                currentState.recordingStartTime = Date.now();
                showRecordingStatus();
                break;
                
            case 'recordingStopped':
                currentState.isRecording = false;
                currentState.recordingStartTime = null;
                hideRecordingStatus();
                break;
                
            case 'recordingStatus':
                // 处理状态更新
                break;
        }
    } catch (error) {
        console.error('处理消息失败:', error);
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initPopup);

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (currentState.recordingTimer) {
        clearInterval(currentState.recordingTimer);
    }
});