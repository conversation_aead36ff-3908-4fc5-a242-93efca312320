/* 弹出窗口样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    width: 380px;
    max-height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 5px;
}

.logo .icon {
    font-size: 24px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

/* 主内容区域 */
.main {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.section {
    margin-bottom: 25px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

/* 状态样式 */
.status-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.status-item .label {
    font-weight: 500;
    color: #666;
}

.status-item .value {
    font-weight: 600;
    color: #333;
}

/* 按钮样式 */
.control-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.btn {
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #333;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 234, 167, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
    color: white;
    grid-column: 1 / -1;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 118, 117, 0.3);
}

/* 录制状态 */
.recording-status {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 12px;
    text-align: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
}

.recording-dot {
    width: 12px;
    height: 12px;
    background: #e53e3e;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.status-text {
    font-weight: 600;
    color: #e53e3e;
}

.recording-time {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    font-family: 'Courier New', monospace;
}

/* 音频源选择 */
.source-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.source-option {
    position: relative;
    cursor: pointer;
}

.source-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.source-option input[type="radio"]:checked + .option-content {
    border-color: #667eea;
    background: #f7fafc;
}

.option-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.option-icon {
    font-size: 20px;
}

.option-info {
    flex: 1;
}

.option-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.option-desc {
    font-size: 12px;
    color: #666;
}

/* 设置样式 */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.setting-label {
    font-weight: 500;
    color: #666;
    font-size: 14px;
}

.setting-select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.setting-select:focus {
    outline: none;
    border-color: #667eea;
}

/* 帮助样式 */
.help-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.help-item {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.help-number {
    width: 24px;
    height: 24px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.help-text {
    flex: 1;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.help-text strong {
    color: #333;
    font-weight: 600;
}

/* 错误样式 */
.error-section {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
}

.error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    margin-top: 10px;
}

/* 页脚样式 */
.footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.footer-links {
    margin-bottom: 10px;
}

.footer-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #764ba2;
}

.separator {
    color: #cbd5e0;
    margin: 0 8px;
}

.footer-info {
    font-size: 11px;
    color: #718096;
}

/* 滚动条样式 */
.main::-webkit-scrollbar {
    width: 6px;
}

.main::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.main::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.main::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 400px) {
    .container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    .control-buttons {
        grid-template-columns: 1fr;
    }
    
    .btn-danger {
        grid-column: 1;
    }
}