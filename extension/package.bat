@echo off
REM =====================================================================
REM 系统音频录制助手 - Chrome扩展程序打包脚本 (Windows版本)
REM 功能：将扩展程序打包为.crx文件，便于分发和安装
REM 使用方法：双击运行或在命令行执行 package.bat
REM =====================================================================

echo ===========================================
echo 系统音频录制助手 - 扩展程序打包脚本 (Windows)
echo ===========================================
echo.

REM 检查必要文件
echo 检查扩展程序文件...
if not exist "manifest.json" (
    echo 错误：缺少 manifest.json 文件
    pause
    exit /b 1
)

if not exist "background.js" (
    echo 错误：缺少 background.js 文件
    pause
    exit /b 1
)

if not exist "content.js" (
    echo 错误：缺少 content.js 文件
    pause
    exit /b 1
)

echo ✓ 所有必要文件已就绪

REM 获取扩展程序信息
echo 读取扩展程序信息...
for /f "tokens=2 delims=," %%a in ('findstr /C:"\"name\":" manifest.json') do (
    set EXTENSION_NAME=%%a
)
set EXTENSION_NAME=%EXTENSION_NAME:"=%
set EXTENSION_NAME=%EXTENSION_NAME: =%

for /f "tokens=2 delims=," %%a in ('findstr /C:"\"version\":" manifest.json') do (
    set EXTENSION_VERSION=%%a
)
set EXTENSION_VERSION=%EXTENSION_VERSION:"=%
set EXTENSION_VERSION=%EXTENSION_VERSION: =%

echo 扩展程序名称：%EXTENSION_NAME%
echo 扩展程序版本：%EXTENSION_VERSION%

REM 创建打包目录
set PACKAGE_DIR=package
if exist "%PACKAGE_DIR%" (
    echo 清理旧打包目录...
    rmdir /s /q "%PACKAGE_DIR%"
)

mkdir "%PACKAGE_DIR%"

REM 复制所有文件到打包目录
echo 复制扩展程序文件...
xcopy /E /I /Y . "%PACKAGE_DIR%\" >nul
cd "%PACKAGE_DIR%"

REM 删除不需要的文件
echo 清理不需要的文件...
del /Q *.sh *.bat *.md 2>nul
for /d /r . %%d in (.git) do @if exist "%%d" rd /s /q "%%d" 2>nul
for /d /r . %%d in (node_modules) do @if exist "%%d" rd /s /q "%%d" 2>nul

REM 创建ZIP文件
echo 创建ZIP文件...
set ZIP_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.zip
powershell -Command "Compress-Archive -Path * -DestinationPath '%ZIP_FILE%' -Force"

REM 检查OpenSSL是否可用
echo 检查OpenSSL...
openssl version >nul 2>&1
if errorlevel 1 (
    echo.
    echo 警告：未找到OpenSSL，将仅创建ZIP文件
    echo 您可以手动将ZIP文件重命名为.crx文件进行安装
    echo.
    goto :end_package
)

REM 生成私钥（如果不存在）
set PRIVATE_KEY=..\extension_key.pem
if not exist "%PRIVATE_KEY%" (
    echo 生成私钥文件...
    openssl genrsa -out "%PRIVATE_KEY%" 2048
    echo ✓ 私钥已生成：%PRIVATE_KEY%
    echo ⚠️  请妥善保管此私钥文件，用于后续版本更新
) else (
    echo 使用现有私钥：%PRIVATE_KEY%
)

REM 创建CRX文件
echo 创建CRX文件...
set CRX_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.crx

REM 使用PowerShell创建CRX文件
powershell -Command ^
    "$zipBytes = [System.IO.File]::ReadAllBytes('%ZIP_FILE%'); "^
    "$keyBytes = [System.IO.File]::ReadAllBytes('%PRIVATE_KEY%'); "^
    "$rsa = [System.Security.Cryptography.RSACryptoServiceProvider]::new(); "^
    "$rsa.ImportCspBlob($keyBytes); "^
    "$pubKeyBytes = $rsa.ExportCspBlob($false); "^
    "$sigBytes = $rsa.SignData($zipBytes, 'SHA256'); "^
    "$crxBytes = [System.Text.Encoding]::UTF8.GetBytes('Cr24') + [System.BitConverter]::GetBytes([UInt32]2) + [System.BitConverter]::GetBytes([UInt32]$pubKeyBytes.Length) + [System.BitConverter]::GetBytes([UInt32]$sigBytes.Length) + $pubKeyBytes + $sigBytes + $zipBytes; "^
    "[System.IO.File]::WriteAllBytes('%CRX_FILE%', $crxBytes)"

:end_package
cd ..

echo ===========================================
echo 打包完成！
echo ===========================================
echo.
echo 生成的文件：
if exist "%PACKAGE_DIR%\%CRX_FILE%" (
    echo 📦 CRX文件：%PACKAGE_DIR%\%CRX_FILE%
)
echo 📦 ZIP文件：%PACKAGE_DIR%\%ZIP_FILE%
if exist "%PRIVATE_KEY%" (
    echo 🔑 私钥文件：%PRIVATE_KEY%
)
echo.
echo 安装方法：
echo 1. 将 .crx 文件拖拽到Chrome扩展程序页面
echo 2. 或者在扩展程序页面点击"加载已解压的扩展程序"
echo 3. 对于ZIP文件，可以重命名为.crx后安装
echo.
echo ⚠️  重要提醒：
if exist "%PRIVATE_KEY%" (
    echo - 请妥善保管 %PRIVATE_KEY% 文件
    echo - 后续更新扩展程序需要使用相同的私钥
)
echo - 分发时只需要.crx文件，不需要私钥
echo.

REM 询问是否要测试安装
set /p "test_install=是否要打开Chrome扩展程序页面进行测试安装？(y/n): "
if /i "%test_install%"=="y" (
    echo 正在打开Chrome扩展程序页面...
    start chrome://extensions/
)

echo 🎉 打包脚本执行完成！
pause