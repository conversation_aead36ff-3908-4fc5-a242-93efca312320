# 图标文件说明

本目录需要包含以下尺寸的PNG图标文件：

## 必需的图标文件

- **icon16.png** - 16x16像素 - 扩展程序工具栏图标
- **icon32.png** - 32x32像素 - 扩展程序管理页面图标
- **icon48.png** - 48x48像素 - Chrome应用商店图标
- **icon128.png** - 128x128像素 - Chrome应用商店图标

## 图标设计建议

### 设计风格
- 使用简洁、现代的设计风格
- 确保在小尺寸下也能清晰识别
- 使用与音频相关的视觉元素（如麦克风、音频波形、音符等）

### 颜色方案
- 主色调：蓝色或紫色（与技术相关）
- 辅助色：白色或浅灰色
- 确保与Chrome界面风格协调

### 视觉元素
- 建议包含音频相关的图标元素
- 可以考虑使用：麦克风、音频波形、音符、声波等
- 确保图标在不同背景下都能清晰显示

## 创建图标的方法

### 使用在线工具
1. **Favicon.io** - 提供图标生成工具
2. **Canva** - 在线设计工具
3. **Adobe Express** - 免费设计工具

### 使用设计软件
1. **Adobe Illustrator** - 矢量图形设计
2. **Figma** - 在线协作设计工具
3. **GIMP** - 免费图像编辑软件

### 使用代码生成
```javascript
// 使用Canvas生成简单图标
const canvas = document.createElement('canvas');
canvas.width = 128;
canvas.height = 128;
const ctx = canvas.getContext('2d');

// 绘制背景
ctx.fillStyle = '#667eea';
ctx.fillRect(0, 0, 128, 128);

// 绘制音频波形
ctx.strokeStyle = '#ffffff';
ctx.lineWidth = 4;
ctx.beginPath();
// 绘制波形路径
ctx.stroke();
```

## 图标规范

### Chrome扩展程序图标要求
- 文件格式：PNG
- 透明背景：支持透明背景
- 颜色模式：RGB + Alpha通道
- 最大文件大小：10MB

### 最佳实践
1. **保持简洁** - 避免过多细节
2. **高对比度** - 确保在各种背景下都能看清
3. **一致性** - 所有尺寸的图标保持一致的设计风格
4. **测试显示** - 在不同设备和背景下测试图标显示效果

## 临时解决方案

在开发阶段，您可以使用以下方法临时创建图标：

1. **使用在线图标库**
   - FontAwesome图标
   - Material Design图标
   - 将SVG转换为PNG

2. **使用文字图标**
   - 创建包含文字的简单图标
   - 使用"🎵"或"🎤"等emoji

3. **使用占位符**
   - 使用纯色块作为临时图标
   - 后期替换为正式图标

## 文件命名约定

- 使用小写字母
- 使用连字符分隔单词
- 包含尺寸信息（可选）
- 示例：`icon-16.png`, `audio-capture-32.png`

## 更新图标

当需要更新图标时：
1. 保持文件名不变
2. 确保新图标与旧图标风格一致
3. 测试所有尺寸的显示效果
4. 更新扩展程序版本号

---

**注意**：正式发布前请确保所有图标文件都已创建并符合Chrome扩展程序的规范要求。