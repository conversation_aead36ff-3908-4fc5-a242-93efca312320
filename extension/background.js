/**
 * Chrome扩展程序后台脚本
 * 处理系统音频捕获和消息传递
 */

let audioStream = null;
let audioContext = null;
let mediaStreamSource = null;
let isRecording = false;
let currentTabId = null;
let audioProcessor = null;

// 音频处理参数
const AUDIO_CONFIG = {
  sampleRate: 48000,
  channelCount: 2,
  bufferSize: 4096,
  bitsPerSecond: 320000
};

/**
 * 初始化音频上下文
 */
function initAudioContext() {
  if (!audioContext) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  }
  return audioContext;
}

/**
 * 捕获标签页音频
 */
async function captureTabAudio(tabId) {
  try {
    console.log('开始捕获标签页音频:', tabId);
    
    const stream = await chrome.tabCapture.capture({
      audio: true,
      video: false,
      audioConstraints: {
        mandatory: {
          chromeMediaSource: 'tab',
          chromeMediaSourceId: tabId.toString(),
          echoCancellation: true,
          autoGainControl: true,
          noiseSuppression: true,
          sampleRate: AUDIO_CONFIG.sampleRate,
          channelCount: AUDIO_CONFIG.channelCount
        }
      }
    });

    if (!stream || !stream.getAudioTracks().length) {
      throw new Error('无法获取音频流');
    }

    console.log('成功获取音频流，轨道数量:', stream.getAudioTracks().length);
    return stream;
  } catch (error) {
    console.error('捕获标签页音频失败:', error);
    throw error;
  }
}

/**
 * 捕获桌面音频
 */
async function captureDesktopAudio() {
  try {
    console.log('开始捕获桌面音频');
    
    // 获取桌面音频源
    const sources = await chrome.desktopCapture.chooseDesktopMedia(['audio'], null);
    
    if (sources === undefined || sources === null) {
      throw new Error('用户取消了音频源选择');
    }

    const constraints = {
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: sources.toString(),
          echoCancellation: true,
          autoGainControl: true,
          noiseSuppression: true,
          sampleRate: AUDIO_CONFIG.sampleRate,
          channelCount: AUDIO_CONFIG.channelCount
        }
      }
    };

    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    
    if (!stream || !stream.getAudioTracks().length) {
      throw new Error('无法获取桌面音频流');
    }

    console.log('成功获取桌面音频流');
    return stream;
  } catch (error) {
    console.error('捕获桌面音频失败:', error);
    throw error;
  }
}

/**
 * 处理音频流
 */
function processAudioStream(stream, targetTabId) {
  try {
    console.log('开始处理音频流');
    
    const ctx = initAudioContext();
    const source = ctx.createMediaStreamSource(stream);
    
    // 创建音频处理器
    const processor = ctx.createScriptProcessor(AUDIO_CONFIG.bufferSize, AUDIO_CONFIG.channelCount, AUDIO_CONFIG.channelCount);
    
    processor.onaudioprocess = (event) => {
      if (!isRecording) return;
      
      const inputData = event.inputBuffer;
      const audioData = [];
      
      // 收集音频数据
      for (let channel = 0; channel < inputData.numberOfChannels; channel++) {
        audioData.push(inputData.getChannelData(channel).slice());
      }
      
      // 发送音频数据到内容脚本
      if (targetTabId) {
        chrome.tabs.sendMessage(targetTabId, {
          type: 'audioData',
          data: audioData,
          timestamp: Date.now(),
          sampleRate: AUDIO_CONFIG.sampleRate,
          channelCount: AUDIO_CONFIG.channelCount
        }).catch(err => {
          console.warn('发送音频数据失败:', err);
        });
      }
    };
    
    source.connect(processor);
    processor.connect(ctx.destination);
    
    audioProcessor = processor;
    mediaStreamSource = source;
    
    console.log('音频流处理已启动');
    return true;
  } catch (error) {
    console.error('处理音频流失败:', error);
    return false;
  }
}

/**
 * 开始音频录制
 */
async function startRecording(tabId, sourceType = 'tab') {
  try {
    if (isRecording) {
      console.warn('已经在录制中');
      return { success: false, error: '已经在录制中' };
    }
    
    console.log('开始音频录制:', { tabId, sourceType });
    
    let stream;
    if (sourceType === 'tab') {
      stream = await captureTabAudio(tabId);
    } else if (sourceType === 'desktop') {
      stream = await captureDesktopAudio();
    } else {
      throw new Error('不支持的音频源类型');
    }
    
    audioStream = stream;
    currentTabId = tabId;
    
    // 处理音频流
    const processResult = processAudioStream(stream, tabId);
    if (!processResult) {
      throw new Error('音频流处理失败');
    }
    
    isRecording = true;
    
    // 通知内容脚本录制开始
    chrome.tabs.sendMessage(tabId, {
      type: 'recordingStarted',
      sourceType: sourceType,
      config: AUDIO_CONFIG
    }).catch(err => {
      console.warn('通知录制开始失败:', err);
    });
    
    console.log('音频录制已开始');
    return { success: true };
  } catch (error) {
    console.error('开始录制失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 停止音频录制
 */
function stopRecording() {
  try {
    if (!isRecording) {
      console.warn('没有在录制中');
      return { success: false, error: '没有在录制中' };
    }
    
    console.log('停止音频录制');
    
    // 停止音频处理
    if (audioProcessor) {
      audioProcessor.disconnect();
      audioProcessor = null;
    }
    
    if (mediaStreamSource) {
      mediaStreamSource.disconnect();
      mediaStreamSource = null;
    }
    
    // 停止音频流
    if (audioStream) {
      audioStream.getTracks().forEach(track => track.stop());
      audioStream = null;
    }
    
    isRecording = false;
    
    // 通知内容脚本录制停止
    if (currentTabId) {
      chrome.tabs.sendMessage(currentTabId, {
        type: 'recordingStopped'
      }).catch(err => {
        console.warn('通知录制停止失败:', err);
      });
    }
    
    currentTabId = null;
    
    console.log('音频录制已停止');
    return { success: true };
  } catch (error) {
    console.error('停止录制失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取录制状态
 */
function getRecordingStatus() {
  return {
    isRecording,
    currentTabId,
    config: AUDIO_CONFIG
  };
}

/**
 * 检查页面是否支持音频录制
 */
async function checkPageSupport(tabId) {
  try {
    const tab = await chrome.tabs.get(tabId);
    const url = new URL(tab.url);
    
    // 检查URL是否支持
    const supportedDomains = ['localhost', '127.0.0.1'];
    const isSupported = supportedDomains.includes(url.hostname) || 
                       url.protocol === 'http:' || 
                       url.protocol === 'https:';
    
    return { 
      supported: isSupported,
      url: tab.url,
      title: tab.title
    };
  } catch (error) {
    console.error('检查页面支持失败:', error);
    return { supported: false, error: error.message };
  }
}

// 消息监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('📨 后台脚本收到消息:', request);
  console.log('📤 发送者信息:', sender);
  console.log('🔧 标签页ID:', sender.tab ? sender.tab.id : '无标签页信息');
  
  try {
    switch (request.type) {
      case 'startRecording':
        if (!sender.tab || !sender.tab.id) {
          sendResponse({ success: false, error: '无法获取标签页信息' });
          return true;
        }
        startRecording(sender.tab.id, request.sourceType)
          .then(sendResponse)
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
        
      case 'stopRecording':
        const stopResult = stopRecording();
        sendResponse(stopResult);
        return true;
        
      case 'getRecordingStatus':
        const status = getRecordingStatus();
        sendResponse(status);
        return true;
        
      case 'checkPageSupport':
        if (!sender.tab || !sender.tab.id) {
          sendResponse({ supported: false, error: '无法获取标签页信息' });
          return true;
        }
        checkPageSupport(sender.tab.id)
          .then(sendResponse)
          .catch(error => sendResponse({ supported: false, error: error.message }));
        return true;
        
      default:
        console.warn('未知消息类型:', request.type);
        sendResponse({ success: false, error: '未知消息类型' });
        return true;
    }
  } catch (error) {
    console.error('处理消息失败:', error);
    sendResponse({ success: false, error: error.message });
    return true;
  }
});

// 扩展程序启动时的初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('扩展程序启动');
});

chrome.runtime.onInstalled.addListener((details) => {
  console.log('扩展程序安装/更新:', details.reason);
});

// 清理资源
chrome.runtime.onSuspend.addListener(() => {
  console.log('扩展程序暂停，清理资源');
  if (isRecording) {
    stopRecording();
  }
});

console.log('系统音频录制助手后台脚本已加载');