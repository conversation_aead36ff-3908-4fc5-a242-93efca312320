@echo off
REM =====================================================================
REM 系统音频录制助手 - 一键打包和安装脚本 (Windows版本)
REM 功能：自动打包扩展程序并打开Chrome进行安装
REM 使用方法：双击运行或在命令行执行 package_and_install.bat
REM =====================================================================

echo ===========================================
echo 系统音频录制助手 - 一键打包和安装 (Windows)
echo ===========================================
echo.

REM 检查必要文件
echo 检查扩展程序文件...
if not exist "manifest.json" (
    echo 错误：缺少 manifest.json 文件
    pause
    exit /b 1
)

if not exist "background.js" (
    echo 错误：缺少 background.js 文件
    pause
    exit /b 1
)

if not exist "content.js" (
    echo 错误：缺少 content.js 文件
    pause
    exit /b 1
)

echo ✓ 所有必要文件已就绪

REM 获取扩展程序信息
echo 读取扩展程序信息...
for /f "tokens=2 delims=," %%a in ('findstr /C:"\"name\":" manifest.json') do (
    set EXTENSION_NAME=%%a
)
set EXTENSION_NAME=%EXTENSION_NAME:"=%
set EXTENSION_NAME=%EXTENSION_NAME: =%

for /f "tokens=2 delims=," %%a in ('findstr /C:"\"version\":" manifest.json') do (
    set EXTENSION_VERSION=%%a
)
set EXTENSION_VERSION=%EXTENSION_VERSION:"=%
set EXTENSION_VERSION=%EXTENSION_VERSION: =%

echo 扩展程序名称：%EXTENSION_NAME%
echo 扩展程序版本：%EXTENSION_VERSION%

REM 创建临时目录
set TEMP_DIR=%TEMP%\extension_package_%RANDOM%
mkdir "%TEMP_DIR%"

REM 复制所有文件到临时目录
echo 复制扩展程序文件...
xcopy /E /I /Y . "%TEMP_DIR%\" >nul
cd "%TEMP_DIR%"

REM 删除不需要的文件
echo 清理不需要的文件...
del /Q *.sh *.bat *.md 2>nul
for /d /r . %%d in (.git) do @if exist "%%d" rd /s /q "%%d" 2>nul
for /d /r . %%d in (node_modules) do @if exist "%%d" rd /s /q "%%d" 2>nul

REM 创建ZIP文件
echo 创建ZIP文件...
set ZIP_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.zip
powershell -Command "Compress-Archive -Path * -DestinationPath '%ZIP_FILE%' -Force"

REM 检查OpenSSL是否可用
echo 检查OpenSSL...
openssl version >nul 2>&1
if errorlevel 1 (
    echo.
    echo 警告：未找到OpenSSL，将仅创建ZIP文件
    echo 您可以手动将ZIP文件重命名为.crx文件进行安装
    echo.
    goto :skip_crx
)

REM 生成私钥（如果不存在）
set PRIVATE_KEY=..\extension_key.pem
if not exist "%PRIVATE_KEY%" (
    echo 生成私钥文件...
    openssl genrsa -out "%PRIVATE_KEY%" 2048
    echo ✓ 私钥已生成：%PRIVATE_KEY%
    echo ⚠️  请妥善保管此私钥文件，用于后续版本更新
) else (
    echo 使用现有私钥：%PRIVATE_KEY%
)

REM 创建CRX文件
echo 创建CRX文件...
set CRX_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.crx

REM 使用PowerShell创建CRX文件
powershell -Command ^
    "$zipBytes = [System.IO.File]::ReadAllBytes('%ZIP_FILE%'); "^
    "$keyBytes = [System.IO.File]::ReadAllBytes('%PRIVATE_KEY%'); "^
    "$rsa = [System.Security.Cryptography.RSACryptoServiceProvider]::new(); "^
    "$rsa.ImportCspBlob($keyBytes); "^
    "$pubKeyBytes = $rsa.ExportCspBlob($false); "^
    "$sigBytes = $rsa.SignData($zipBytes, 'SHA256'); "^
    "$crxBytes = [System.Text.Encoding]::UTF8.GetBytes('Cr24') + [System.BitConverter]::GetBytes([UInt32]2) + [System.BitConverter]::GetBytes([UInt32]$pubKeyBytes.Length) + [System.BitConverter]::GetBytes([UInt32]$sigBytes.Length) + $pubKeyBytes + $sigBytes + $zipBytes; "^
    "[System.IO.File]::WriteAllBytes('%CRX_FILE%', $crxBytes)"

:skip_crx
REM 复制文件到原目录
copy "%ZIP_FILE%" "%OLDPWD%\" >nul
if exist "%CRX_FILE%" (
    copy "%CRX_FILE%" "%OLDPWD%\" >nul
)

cd "%OLDPWD%"
rmdir /s /q "%TEMP_DIR%"

echo ===========================================
echo 打包完成！
echo ===========================================
echo.
echo 生成的文件：
if exist "%CRX_FILE%" (
    echo 📦 CRX文件：%CRX_FILE%
)
echo 📦 ZIP文件：%ZIP_FILE%
if exist "%PRIVATE_KEY%" (
    echo 🔑 私钥文件：%PRIVATE_KEY%
)
echo.
echo 正在准备安装...

REM 检查Chrome是否可用
where chrome >nul 2>&1
if errorlevel 1 (
    where google-chrome >nul 2>&1
    if errorlevel 1 (
        echo 警告：未找到Chrome浏览器
        echo 请手动打开Chrome并访问：chrome://extensions/
        echo 然后将CRX文件拖拽到扩展程序页面
        goto :manual_install
    )
)

echo 正在打开Chrome扩展程序页面...
start chrome://extensions/

:manual_install
echo.
echo ===========================================
echo 安装步骤
echo ===========================================
echo.

echo 1. 🔧 在Chrome中：
echo    - 启用"开发者模式"（如果尚未启用）
echo    - 将CRX文件拖拽到扩展程序页面
echo.

echo 2. 📦 文件位置：
if exist "%CRX_FILE%" (
    echo    - CRX文件：%CD%\%CRX_FILE%
)
echo    - ZIP文件：%CD%\%ZIP_FILE%
echo.

echo 3. ✅ 安装验证：
echo    - 扩展程序应该出现在列表中
echo    - 状态显示为"已启用"
echo    - 工具栏显示扩展程序图标
echo.

echo ===========================================
echo 安装提示
echo ===========================================
echo.

echo 💡 如果拖拽安装失败：
echo    - 确保"开发者模式"已启用
echo    - 尝试点击"加载已解压的扩展程序"选择CRX文件
echo.

echo 💡 如果提示错误：
echo    - 检查CRX文件是否完整
echo    - 确保Chrome版本支持
echo    - 尝试重新打包
echo.

echo 💡 分发说明：
if exist "%CRX_FILE%" (
    echo    - 将 %CRX_FILE% 分发给其他用户
    echo    - 用户可以直接拖拽安装
    echo    - 无需启用开发者模式
) else (
    echo    - 将 %ZIP_FILE% 分发给其他用户
    echo    - 用户可以重命名为.crx后安装
)

echo.
echo ===========================================
echo 打包和安装准备完成！
echo ===========================================
echo 请按照上述步骤完成Chrome扩展程序的安装。
echo 如有问题请查看README.md文档或联系技术支持。
echo.
pause