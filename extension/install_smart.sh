#!/bin/bash

# =====================================================================
# 系统音频录制助手 - 智能安装脚本
# 功能：自动化程度更高的安装脚本，减少用户手动操作
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 智能安装脚本"
echo "==========================================="
echo

# 获取当前文件夹的绝对路径
CURRENT_DIR=$(pwd)
echo "扩展程序路径：$CURRENT_DIR"

# 检查Chrome浏览器
echo "正在检测Chrome浏览器..."
CHROME_CMD=""

if command -v google-chrome &> /dev/null; then
    CHROME_CMD="google-chrome"
elif command -v google-chrome-stable &> /dev/null; then
    CHROME_CMD="google-chrome-stable"
elif command -v chromium-browser &> /dev/null; then
    CHROME_CMD="chromium-browser"
elif command -v chromium &> /dev/null; then
    CHROME_CMD="chromium"
fi

if [ -z "$CHROME_CMD" ] && [ -d "/Applications/Google Chrome.app" ]; then
    CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

if [ -z "$CHROME_CMD" ]; then
    echo "错误：未找到Chrome浏览器"
    echo "请手动打开Chrome并访问：chrome://extensions/"
    exit 1
fi

echo "发现浏览器：$CHROME_CMD"

# 检查文件完整性
echo "正在检查扩展程序文件..."
REQUIRED_FILES=("manifest.json" "background.js" "content.js")
MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "错误：缺少必要文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    exit 1
fi

echo "✓ 所有必要文件已就绪"

# 复制路径到剪贴板
echo "正在复制路径到剪贴板..."
echo "$CURRENT_DIR" | pbcopy

echo
echo "==========================================="
echo "智能安装步骤"
echo "==========================================="
echo

echo "脚本已为您准备好了以下内容："
echo "✓ 检测到Chrome浏览器"
echo "✓ 验证了扩展程序文件完整性"
echo "✓ 复制了扩展程序路径到剪贴板"
echo

echo "现在请按照以下简单步骤完成安装："
echo

echo "1. 🔄 正在打开Chrome扩展程序页面..."
"$CHROME_CMD" chrome://extensions/ &
sleep 2

echo "2. 🔧 在Chrome中："
echo "   - 点击页面右上角的'开发者模式'开关"
echo "   - 点击'加载已解压的扩展程序'按钮"
echo

echo "3. 📁 在文件选择器中："
echo "   - 按Cmd+V粘贴路径（或手动导航到）"
echo "   - 选择文件夹：$CURRENT_DIR"
echo "   - 点击'选择文件夹'"
echo

echo "4. ✅ 验证安装："
echo "   - 扩展程序应该出现在列表中"
echo "   - 状态显示为'已启用'"
echo "   - 工具栏显示扩展程序图标"
echo

echo "==========================================="
echo "快速提示"
echo "==========================================="
echo

echo "💡 如果文件选择器没有打开剪贴板路径："
echo "   - 手动导航到：$CURRENT_DIR"
echo

echo "💡 如果扩展程序加载失败："
echo "   - 检查manifest.json文件格式"
echo "   - 确保Chrome版本支持"
echo "   - 尝试重新加载扩展程序"
echo

echo "💡 测试扩展程序："
echo "   - 点击工具栏中的扩展程序图标"
echo "   - 在支持的网页中测试音频录制功能"
echo

echo "==========================================="
echo "安装完成检查清单"
echo "==========================================="
echo

echo "请确认以下项目："
echo "[ ] 开发者模式已启用"
echo "[ ] 扩展程序已加载并启用"
echo "[ ] 扩展程序图标显示在工具栏"
echo "[ ] 点击图标可以打开弹出窗口"
echo "[ ] 网页中的音频录制组件显示连接状态"
echo

# 等待用户确认
echo
read -p "按回车键打开扩展程序文件夹进行验证..."
open "$CURRENT_DIR"

echo
echo "🎉 安装脚本执行完成！"
echo "请按照上述步骤完成Chrome扩展程序的手动确认部分。"
echo "如有问题，路径已复制到剪贴板，可随时使用Cmd+V粘贴。"