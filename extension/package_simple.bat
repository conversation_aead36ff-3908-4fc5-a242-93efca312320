@echo off
REM =====================================================================
REM 系统音频录制助手 - 简化的Chrome扩展程序打包脚本 (Windows版本)
REM 功能：创建ZIP格式的扩展程序包，可直接重命名为.crx
REM 使用方法：双击运行或在命令行执行 package_simple.bat
REM =====================================================================

echo ===========================================
echo 系统音频录制助手 - 简化打包脚本 (Windows)
echo ===========================================
echo.

REM 检查必要文件
echo 检查扩展程序文件...
if not exist "manifest.json" (
    echo 错误：缺少 manifest.json 文件
    pause
    exit /b 1
)

if not exist "background.js" (
    echo 错误：缺少 background.js 文件
    pause
    exit /b 1
)

if not exist "content.js" (
    echo 错误：缺少 content.js 文件
    pause
    exit /b 1
)

echo ✓ 所有必要文件已就绪

REM 获取扩展程序信息
echo 读取扩展程序信息...
for /f "tokens=2 delims=," %%a in ('findstr /C:"\"name\":" manifest.json') do (
    set EXTENSION_NAME=%%a
)
set EXTENSION_NAME=%EXTENSION_NAME:"=%
set EXTENSION_NAME=%EXTENSION_NAME: =%

for /f "tokens=2 delims=," %%a in ('findstr /C:"\"version\":" manifest.json') do (
    set EXTENSION_VERSION=%%a
)
set EXTENSION_VERSION=%EXTENSION_VERSION:"=%
set EXTENSION_VERSION=%EXTENSION_VERSION: =%

echo 扩展程序名称：%EXTENSION_NAME%
echo 扩展程序版本：%EXTENSION_VERSION%

REM 创建打包目录
set PACKAGE_DIR=dist
if exist "%PACKAGE_DIR%" (
    echo 清理旧打包目录...
    rmdir /s /q "%PACKAGE_DIR%"
)

mkdir "%PACKAGE_DIR%"

REM 复制所有文件到打包目录
echo 复制扩展程序文件...
xcopy /E /I /Y . "%PACKAGE_DIR%\" >nul
cd "%PACKAGE_DIR%"

REM 删除不需要的文件
echo 清理不需要的文件...
del /Q *.sh *.bat *.md *.crx *.zip 2>nul
for /d /r . %%d in (.git) do @if exist "%%d" rd /s /q "%%d" 2>nul
for /d /r . %%d in (node_modules) do @if exist "%%d" rd /s /q "%%d" 2>nul

REM 创建ZIP文件
echo 创建ZIP文件...
set ZIP_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.zip
powershell -Command "Compress-Archive -Path * -DestinationPath '%ZIP_FILE%' -Force"

REM 创建CRX文件（简单重命名）
echo 创建CRX文件...
set CRX_FILE=%EXTENSION_NAME%_%EXTENSION_VERSION%.crx
copy "%ZIP_FILE%" "%CRX_FILE%" >nul

cd ..

echo ===========================================
echo 打包完成！
echo ===========================================
echo.
echo 生成的文件：
echo 📦 ZIP文件：%PACKAGE_DIR%\%ZIP_FILE%
echo 📦 CRX文件：%PACKAGE_DIR%\%CRX_FILE%
echo.
echo 安装方法：
echo 方法1（推荐）：
echo 1. 将 %CRX_FILE% 拖拽到Chrome扩展程序页面
echo 2. 确认安装即可
echo.
echo 方法2：
echo 1. 打开Chrome扩展程序页面：chrome://extensions/
echo 2. 启用"开发者模式"
echo 3. 点击"加载已解压的扩展程序"
echo 4. 选择 %PACKAGE_DIR% 文件夹
echo.
echo 注意：
echo - ZIP文件可以直接重命名为.crx文件使用
echo - 这种方法生成的CRX文件没有数字签名
echo - 适合开发测试和内部分发
echo.

REM 询问是否要测试安装
set /p "test_install=是否要打开Chrome扩展程序页面进行测试安装？(y/n): "
if /i "%test_install%"=="y" (
    echo 正在打开Chrome扩展程序页面...
    start chrome://extensions/
)

echo 🎉 打包脚本执行完成！
pause