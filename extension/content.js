/**
 * Chrome扩展程序内容脚本
 * 处理与网页的通信和音频数据接收
 */

// 音频录制状态
let recordingState = {
  isRecording: false,
  sourceType: null,
  startTime: null,
  audioChunks: [],
  config: null
};

// 音频处理相关
let audioContext = null;
let mediaRecorder = null;
let audioBufferQueue = [];
let isProcessing = false;

/**
 * 初始化音频上下文
 */
function initAudioContext() {
  if (!audioContext) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  }
  return audioContext;
}

/**
 * 将音频数据转换为Blob
 */
function audioDataToBlob(audioData, config) {
  try {
    // 创建离线音频上下文
    const ctx = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(
      config.channelCount,
      audioData[0].length,
      config.sampleRate
    );
    
    // 创建音频缓冲区
    const buffer = ctx.createBuffer(config.channelCount, audioData[0].length, config.sampleRate);
    
    // 填充音频数据
    for (let channel = 0; channel < config.channelCount; channel++) {
      const channelData = buffer.getChannelData(channel);
      channelData.set(audioData[channel]);
    }
    
    // 创建音频源
    const source = ctx.createBufferSource();
    source.buffer = buffer;
    source.connect(ctx.destination);
    
    // 创建MediaRecorder
    const stream = ctx.createMediaStreamDestination().stream;
    const recorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: config.bitsPerSecond || 320000
    });
    
    return recorder;
  } catch (error) {
    console.error('音频数据转换失败:', error);
    return null;
  }
}

/**
 * 处理接收到的音频数据
 */
function processAudioData(audioData, config) {
  try {
    if (!recordingState.isRecording) return;
    
    // 添加到队列
    audioBufferQueue.push({
      data: audioData,
      config: config,
      timestamp: Date.now()
    });
    
    // 如果没有在处理，开始处理
    if (!isProcessing) {
      processAudioQueue();
    }
  } catch (error) {
    console.error('处理音频数据失败:', error);
  }
}

/**
 * 处理音频队列
 */
async function processAudioQueue() {
  if (isProcessing || audioBufferQueue.length === 0) return;
  
  isProcessing = true;
  
  try {
    while (audioBufferQueue.length > 0) {
      const audioItem = audioBufferQueue.shift();
      
      // 创建音频片段
      const audioBlob = await createAudioBlob(audioItem.data, audioItem.config);
      
      if (audioBlob) {
        recordingState.audioChunks.push(audioBlob);
        
        // 发送到网页
        window.postMessage({
          type: 'audioChunk',
          data: audioBlob,
          timestamp: audioItem.timestamp,
          isRecording: recordingState.isRecording
        }, '*');
      }
    }
  } catch (error) {
    console.error('处理音频队列失败:', error);
  } finally {
    isProcessing = false;
  }
}

/**
 * 创建音频Blob
 */
async function createAudioBlob(audioData, config) {
  try {
    const ctx = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(
      config.channelCount,
      audioData[0].length,
      config.sampleRate
    );
    
    const buffer = ctx.createBuffer(config.channelCount, audioData[0].length, config.sampleRate);
    
    for (let channel = 0; channel < config.channelCount; channel++) {
      const channelData = buffer.getChannelData(channel);
      channelData.set(audioData[channel]);
    }
    
    const source = ctx.createBufferSource();
    source.buffer = buffer;
    source.connect(ctx.destination);
    
    // 开始渲染
    const renderedBuffer = await ctx.startRendering();
    
    // 转换为WAV格式
    const wavBlob = audioBufferToWav(renderedBuffer);
    return wavBlob;
  } catch (error) {
    console.error('创建音频Blob失败:', error);
    return null;
  }
}

/**
 * 将AudioBuffer转换为WAV Blob
 */
function audioBufferToWav(buffer) {
  const length = buffer.length * buffer.numberOfChannels * 2 + 44;
  const arrayBuffer = new ArrayBuffer(length);
  const view = new DataView(arrayBuffer);
  const channels = [];
  let offset = 0;
  let pos = 0;
  
  // 写入WAV头部
  setUint32(0x46464952); // "RIFF"
  setUint32(length - 8); // file length - 8
  setUint32(0x45564157); // "WAVE"
  
  setUint32(0x20746d66); // "fmt " chunk
  setUint32(16); // length = 16
  setUint16(1); // PCM
  setUint16(buffer.numberOfChannels);
  setUint32(buffer.sampleRate);
  setUint32(buffer.sampleRate * 2 * buffer.numberOfChannels); // avg. bytes/sec
  setUint16(buffer.numberOfChannels * 2); // block-align
  setUint16(16); // 16-bit
  
  setUint32(0x61746164); // "data" - chunk
  setUint32(length - pos - 4); // chunk length
  
  // 写入音频数据
  for (let i = 0; i < buffer.numberOfChannels; i++) {
    channels.push(buffer.getChannelData(i));
  }
  
  while (pos < length) {
    for (let i = 0; i < buffer.numberOfChannels; i++) {
      let sample = Math.max(-1, Math.min(1, channels[i][offset]));
      sample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(pos, sample, true);
      pos += 2;
    }
    offset++;
  }
  
  return new Blob([arrayBuffer], { type: 'audio/wav' });
  
  function setUint16(data) {
    view.setUint16(pos, data, true);
    pos += 2;
  }
  
  function setUint32(data) {
    view.setUint32(pos, data, true);
    pos += 4;
  }
}

/**
 * 开始录制
 */
function startRecording(sourceType, config) {
  try {
    console.log('内容脚本：开始录制', { sourceType, config });
    
    recordingState = {
      isRecording: true,
      sourceType: sourceType,
      startTime: Date.now(),
      audioChunks: [],
      config: config
    };
    
    audioBufferQueue = [];
    
    // 通知网页录制开始
    window.postMessage({
      type: 'recordingStarted',
      sourceType: sourceType,
      config: config,
      startTime: recordingState.startTime
    }, '*');
    
    console.log('内容脚本：录制已开始');
  } catch (error) {
    console.error('开始录制失败:', error);
  }
}

/**
 * 停止录制
 */
function stopRecording() {
  try {
    console.log('内容脚本：停止录制');
    
    if (!recordingState.isRecording) return;
    
    recordingState.isRecording = false;
    
    // 处理剩余的音频数据
    if (audioBufferQueue.length > 0) {
      processAudioQueue();
    }
    
    // 生成最终音频文件
    const finalBlob = combineAudioChunks();
    
    // 通知网页录制停止
    window.postMessage({
      type: 'recordingStopped',
      audioBlob: finalBlob,
      duration: Date.now() - recordingState.startTime,
      chunksCount: recordingState.audioChunks.length
    }, '*');
    
    // 重置状态
    recordingState = {
      isRecording: false,
      sourceType: null,
      startTime: null,
      audioChunks: [],
      config: null
    };
    
    audioBufferQueue = [];
    
    console.log('内容脚本：录制已停止');
  } catch (error) {
    console.error('停止录制失败:', error);
  }
}

/**
 * 合并音频块
 */
function combineAudioChunks() {
  try {
    if (recordingState.audioChunks.length === 0) return null;
    
    // 简单的合并策略：返回第一个块作为示例
    // 实际应用中应该实现更复杂的音频合并算法
    return recordingState.audioChunks[0];
  } catch (error) {
    console.error('合并音频块失败:', error);
    return null;
  }
}

/**
 * 获取录制状态
 */
function getRecordingStatus() {
  return {
    ...recordingState,
    queueLength: audioBufferQueue.length,
    isProcessing: isProcessing
  };
}

/**
 * 发送消息到后台脚本
 */
function sendMessageToBackground(message) {
  return new Promise((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 检查页面支持
 */
async function checkPageSupport() {
  try {
    const response = await sendMessageToBackground({ type: 'checkPageSupport' });
    return response;
  } catch (error) {
    console.error('检查页面支持失败:', error);
    return { supported: false, error: error.message };
  }
}

/**
 * 初始化内容脚本
 */
function initContentScript() {
  console.log('🎯 系统音频录制助手内容脚本已加载');
  console.log('📍 当前页面:', window.location.href);
  console.log('🔧 Chrome Runtime ID:', chrome.runtime.id);
  
  // 监听来自后台脚本的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 内容脚本收到后台消息:', request);
    console.log('📤 发送者信息:', sender);
    
    try {
      switch (request.type) {
        case 'audioData':
          processAudioData(request.data, {
            sampleRate: request.sampleRate,
            channelCount: request.channelCount,
            bitsPerSecond: 320000
          });
          sendResponse({ success: true });
          break;
          
        case 'recordingStarted':
          startRecording(request.sourceType, request.config);
          sendResponse({ success: true });
          break;
          
        case 'recordingStopped':
          stopRecording();
          sendResponse({ success: true });
          break;
          
        default:
          console.warn('未知消息类型:', request.type);
          sendResponse({ success: false, error: '未知消息类型' });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  });
  
  // 监听来自网页的消息
  window.addEventListener('message', (event) => {
    // 验证消息来源
    if (event.source !== window) return;
    
    const message = event.data;
    if (!message || !message.type) return;
    
    console.log('🌐 内容脚本收到网页消息:', message);
    
    try {
      switch (message.type) {
        case 'startRecording':
          sendMessageToBackground({
            type: 'startRecording',
            sourceType: message.sourceType || 'tab'
          }).then(response => {
            window.postMessage({
              type: 'recordingResponse',
              action: 'start',
              success: response.success,
              error: response.error
            }, '*');
          });
          break;
          
        case 'stopRecording':
          sendMessageToBackground({ type: 'stopRecording' }).then(response => {
            window.postMessage({
              type: 'recordingResponse',
              action: 'stop',
              success: response.success,
              error: response.error
            }, '*');
          });
          break;
          
        case 'getRecordingStatus':
          sendMessageToBackground({ type: 'getRecordingStatus' }).then(response => {
            window.postMessage({
              type: 'recordingStatus',
              background: response,
              content: getRecordingStatus()
            }, '*');
          });
          break;
          
        case 'checkPageSupport':
          checkPageSupport().then(response => {
            window.postMessage({
              type: 'pageSupport',
              ...response
            }, '*');
          });
          break;
      }
    } catch (error) {
      console.error('处理网页消息失败:', error);
    }
  });
  
  // 通知网页内容脚本已准备就绪
  console.log('📤 发送contentScriptReady消息到网页');
  window.postMessage({
    type: 'contentScriptReady',
    version: '1.0.0'
  }, '*');
}

// 启动内容脚本
initContentScript();