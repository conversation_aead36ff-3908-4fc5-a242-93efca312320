#!/bin/bash

# =====================================================================
# 系统音频录制助手 - Chrome扩展程序自动化安装脚本 (Mac版本)
# 使用AppleScript自动化Chrome操作
# 功能：自动开启开发者模式并引导用户完成安装
# =====================================================================

echo "==========================================="
echo "系统音频录制助手 - 自动化安装脚本 (Mac)"
echo "==========================================="
echo

# 检测Chrome浏览器
echo "正在检测Chrome浏览器..."
CHROME_CMD=""

# 检查PATH中的Chrome
if command -v google-chrome &> /dev/null; then
    CHROME_CMD="google-chrome"
elif command -v google-chrome-stable &> /dev/null; then
    CHROME_CMD="google-chrome-stable"
elif command -v chromium-browser &> /dev/null; then
    CHROME_CMD="chromium-browser"
elif command -v chromium &> /dev/null; then
    CHROME_CMD="chromium"
fi

# 如果PATH中没有找到，检查Mac应用程序目录
if [ -z "$CHROME_CMD" ] && [ -d "/Applications/Google Chrome.app" ]; then
    CHROME_CMD="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

# 检查Chromium
if [ -z "$CHROME_CMD" ] && [ -d "/Applications/Chromium.app" ]; then
    CHROME_CMD="/Applications/Chromium.app/Contents/MacOS/Chromium"
fi

# 检查用户目录中的Chrome
if [ -z "$CHROME_CMD" ] && [ -d "$HOME/Applications/Google Chrome.app" ]; then
    CHROME_CMD="$HOME/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

if [ -z "$CHROME_CMD" ]; then
    echo "错误：未找到Chrome或Chromium浏览器"
    echo "请手动打开Chrome浏览器并访问：chrome://extensions/"
    echo "或者安装Chrome浏览器后重新运行此脚本"
    exit 1
fi

echo "发现浏览器：$CHROME_CMD"
echo

# 检查是否已安装扩展程序
echo "检查是否已安装扩展程序..."
EXTENSION_ID=$(grep -o '"id": "[^"]*"' manifest.json | cut -d'"' -f4 | head -1)
if [ -z "$EXTENSION_ID" ]; then
    echo "无法从manifest.json获取扩展程序ID，将使用文件夹名称"
    EXTENSION_NAME=$(basename "$(pwd)")
    echo "扩展程序名称：$EXTENSION_NAME"
else
    echo "扩展程序ID：$EXTENSION_ID"
fi

echo
echo "==========================================="
echo "自动化安装选项"
echo "==========================================="
echo

echo "请选择安装方式："
echo "1. 基础安装（打开扩展程序页面）"
echo "2. 半自动安装（使用AppleScript辅助）"
echo "3. 手动安装指南"
echo

read -p "请输入选择 (1/2/3): " choice

case $choice in
    1)
        echo "正在打开Chrome扩展程序管理页面..."
        "$CHROME_CMD" chrome://extensions/ &
        
        echo
        echo "请按照以下步骤手动安装："
        echo "1. 在Chrome扩展程序页面中，打开'开发者模式'开关"
        echo "2. 点击'加载已解压的扩展程序'按钮"
        echo "3. 选择当前文件夹：$(pwd)"
        echo "4. 扩展程序应该出现在列表中"
        ;;
        
    2)
        echo "正在启动半自动安装..."
        
        # 创建AppleScript来自动化Chrome操作
        cat > /tmp/chrome_extension_installer.scpt << 'EOF'
tell application "System Events"
    -- 等待Chrome启动
    delay 2
    
    -- 查找Chrome窗口
    set chromeFound to false
    repeat with proc in processes
        if name of proc contains "Chrome" then
            set chromeFound to true
            tell proc
                -- 尝试激活窗口
                set frontmost to true
                delay 1
                
                -- 尝试通过键盘快捷键开启开发者模式
                -- 注意：这需要用户已经在扩展程序页面
                keystroke "d" using command down -- Command+D 可能是开发者模式快捷键
                delay 1
                
                -- 显示提示
                display dialog "请手动点击'加载已解压的扩展程序'按钮，然后选择扩展程序文件夹" buttons {"OK"} default button "OK"
            end tell
            exit repeat
        end if
    end repeat
    
    if not chromeFound then
        display dialog "请确保Chrome浏览器已打开并显示扩展程序页面" buttons {"OK"} default button "OK"
    end if
end tell
EOF
        
        # 打开Chrome扩展程序页面
        "$CHROME_CMD" chrome://extensions/ &
        
        echo "Chrome已打开，正在准备自动化操作..."
        echo "请在Chrome中允许自动化权限（如果提示）"
        
        # 等待用户确认
        read -p "按回车键开始自动化操作..."
        
        # 运行AppleScript
        osascript /tmp/chrome_extension_installer.scpt
        
        # 清理临时文件
        rm -f /tmp/chrome_extension_installer.scpt
        
        echo "自动化操作完成，请完成剩余的手动步骤"
        ;;
        
    3)
        echo "==========================================="
        echo "手动安装详细指南"
        echo "==========================================="
        echo
        echo "步骤1：打开Chrome扩展程序管理页面"
        echo "   - 在Chrome地址栏输入：chrome://extensions/"
        echo "   - 或者：Chrome菜单 → 更多工具 → 扩展程序"
        echo
        
        echo "步骤2：启用开发者模式"
        echo "   - 在页面右上角找到'开发者模式'开关"
        echo "   - 点击开关以启用开发者模式"
        echo
        
        echo "步骤3：加载扩展程序"
        echo "   - 点击'加载已解压的扩展程序'按钮"
        echo "   - 在文件选择器中选择文件夹：$(pwd)"
        echo "   - 点击'选择文件夹'按钮"
        echo
        
        echo "步骤4：验证安装"
        echo "   - 扩展程序应该出现在列表中"
        echo "   - 检查扩展程序是否已启用"
        echo "   - 浏览器工具栏应该显示扩展程序图标"
        echo
        
        echo "步骤5：测试功能"
        echo "   - 点击扩展程序图标测试弹出窗口"
        echo "   - 在支持的网页中测试音频录制功能"
        echo
        
        read -p "按回车键打开Chrome扩展程序页面..."
        "$CHROME_CMD" chrome://extensions/ &
        ;;
        
    *)
        echo "无效选择，默认使用基础安装"
        "$CHROME_CMD" chrome://extensions/ &
        
        echo
        echo "请按照以下步骤手动安装："
        echo "1. 在Chrome扩展程序页面中，打开'开发者模式'开关"
        echo "2. 点击'加载已解压的扩展程序'按钮"
        echo "3. 选择当前文件夹：$(pwd)"
        echo "4. 扩展程序应该出现在列表中"
        ;;
esac

echo
echo "==========================================="
echo "安装后验证"
echo "==========================================="
echo

echo "安装完成后，请验证以下内容："
echo "✓ 扩展程序出现在扩展程序列表中"
echo "✓ 扩展程序状态为'已启用'"
echo "✓ 浏览器工具栏显示扩展程序图标"
echo "✓ 点击图标可以打开弹出窗口"
echo

echo "如果遇到问题："
echo "- 检查是否已启用开发者模式"
echo "- 确认选择了正确的文件夹"
echo "- 查看扩展程序页面是否有错误信息"
echo "- 尝试重新加载扩展程序"
echo


echo "==========================================="
echo "安装完成"
echo "==========================================="
echo "感谢使用系统音频录制助手！"
echo "如有问题请查看README.md文档或联系技术支持"