/**
 * 扩展程序测试脚本
 * 用于验证扩展程序是否正常工作
 */

console.log('🔍 开始测试扩展程序...');

// 检查Chrome API
if (!window.chrome || !window.chrome.runtime) {
    console.error('❌ 不在Chrome浏览器中');
    process.exit(1);
}

console.log('✅ Chrome浏览器检测通过');

// 监听来自扩展程序的消息
const messageHandler = (event) => {
    if (event.source !== window) return;
    
    const data = event.data;
    if (!data || !data.type) return;
    
    console.log('📨 收到消息:', data);
    
    switch (data.type) {
        case 'contentScriptReady':
            console.log('🎉 内容脚本已准备就绪');
            break;
            
        case 'pageSupport':
            if (data.supported) {
                console.log('✅ 页面支持音频录制');
            } else {
                console.log('❌ 页面不支持音频录制:', data.error);
            }
            break;
            
        case 'recordingStarted':
            console.log('🎵 录制已开始');
            break;
            
        case 'recordingStopped':
            console.log('⏹️ 录制已停止');
            break;
            
        case 'recordingResponse':
            if (data.success) {
                console.log(`✅ ${data.action} 操作成功`);
            } else {
                console.log(`❌ ${data.action} 操作失败: ${data.error}`);
            }
            break;
            
        default:
            console.log('❓ 未知消息类型:', data.type);
    }
};

// 添加消息监听器
window.addEventListener('message', messageHandler);

// 测试函数
async function testExtension() {
    console.log('🔍 开始测试扩展程序通信...');
    
    // 测试1: 检查页面支持
    console.log('📤 发送页面支持检查...');
    window.postMessage({ type: 'checkPageSupport' }, '*');
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试2: 开始录制
    console.log('📤 发送开始录制消息...');
    window.postMessage({ type: 'startRecording', sourceType: 'tab' }, '*');
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试3: 停止录制
    console.log('📤 发送停止录制消息...');
    window.postMessage({ type: 'stopRecording' }, '*');
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('🏁 测试完成');
    
    // 清理
    window.removeEventListener('message', messageHandler);
}

// 运行测试
testExtension().catch(error => {
    console.error('❌ 测试失败:', error);
    window.removeEventListener('message', messageHandler);
});

// 如果在5秒内没有收到contentScriptReady消息，认为扩展程序未连接
setTimeout(() => {
    console.log('⏰ 等待扩展程序连接超时');
    window.removeEventListener('message', messageHandler);
}, 5000);