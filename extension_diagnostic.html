<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展程序诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            background: #007bff;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .check-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #ccc;
            background: #f9f9f9;
        }
        .check-item.pass {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .check-item.fail {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔧 扩展程序诊断工具</h1>
        <p>这个工具可以帮助诊断Chrome扩展程序的安装和运行状态。</p>
        
        <button onclick="runDiagnostic()">开始诊断</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
        
        <div class="log" id="log">点击"开始诊断"按钮开始检测...
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let resultsElement = document.getElementById('results');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearResults() {
            logElement.textContent = '';
            resultsElement.innerHTML = '';
        }
        
        function addCheckResult(title, passed, details) {
            const checkItem = document.createElement('div');
            checkItem.className = `check-item ${passed ? 'pass' : 'fail'}`;
            checkItem.innerHTML = `
                <strong>${passed ? '✅' : '❌'} ${title}</strong>
                <div style="margin-top: 5px; font-size: 12px;">${details}</div>
            `;
            resultsElement.appendChild(checkItem);
        }
        
        function runDiagnostic() {
            clearResults();
            addLog('🔍 开始扩展程序诊断...');
            
            // 基本环境检查
            addLog('📋 检查基本环境...');
            
            // 1. 检查浏览器类型
            const userAgent = navigator.userAgent;
            const isChrome = userAgent.includes('Chrome') && !userAgent.includes('Edg');
            addCheckResult(
                '浏览器类型检查',
                isChrome,
                `User Agent: ${userAgent}`
            );
            addLog(`🌐 浏览器: ${isChrome ? 'Chrome' : '非Chrome浏览器'}`);
            
            // 2. 检查页面协议
            const protocol = window.location.protocol;
            const isFileProtocol = protocol === 'file:';
            addCheckResult(
                '页面协议检查',
                isFileProtocol,
                `当前协议: ${protocol}, 页面: ${window.location.href}`
            );
            addLog(`📍 页面协议: ${protocol}`);
            
            // 3. 检查Chrome对象
            const hasChromeObject = typeof window.chrome !== 'undefined';
            addCheckResult(
                'Chrome对象存在性',
                hasChromeObject,
                `window.chrome: ${hasChromeObject ? '存在' : '不存在'}`
            );
            addLog(`🔧 Chrome对象: ${hasChromeObject ? '存在' : '不存在'}`);
            
            // 4. 检查Chrome Runtime
            const hasChromeRuntime = hasChromeObject && typeof window.chrome.runtime !== 'undefined';
            addCheckResult(
                'Chrome Runtime检查',
                hasChromeRuntime,
                `window.chrome.runtime: ${hasChromeRuntime ? '存在' : '不存在'}`
            );
            addLog(`⚙️ Chrome Runtime: ${hasChromeRuntime ? '存在' : '不存在'}`);
            
            if (hasChromeRuntime) {
                // 5. 检查扩展程序ID
                const extensionId = chrome.runtime.id;
                addCheckResult(
                    '扩展程序ID',
                    !!extensionId,
                    `Extension ID: ${extensionId || '无'}`
                );
                addLog(`🆔 扩展程序ID: ${extensionId || '无'}`);
                
                // 6. 检查扩展程序清单
                try {
                    const manifest = chrome.runtime.getManifest();
                    addCheckResult(
                        '扩展程序清单',
                        !!manifest,
                        `名称: ${manifest?.name || '无'}, 版本: ${manifest?.version || '无'}`
                    );
                    addLog(`📋 扩展程序: ${manifest?.name || '无'} v${manifest?.version || '无'}`);
                } catch (error) {
                    addCheckResult(
                        '扩展程序清单',
                        false,
                        `错误: ${error.message}`
                    );
                    addLog(`❌ 获取清单失败: ${error.message}`);
                }
            }
            
            // 7. 检查内容脚本注入
            addLog('📤 测试内容脚本通信...');
            let contentScriptResponded = false;
            
            // 监听内容脚本响应
            const messageHandler = (event) => {
                if (event.source !== window) return;
                const data = event.data;
                if (!data || !data.type) return;
                
                addLog(`📨 收到消息: ${data.type}`);
                
                if (data.type === 'contentScriptReady') {
                    contentScriptResponded = true;
                    addCheckResult(
                        '内容脚本响应',
                        true,
                        `版本: ${data.version || '未知'}`
                    );
                    addLog(`✅ 内容脚本已响应`);
                    window.removeEventListener('message', messageHandler);
                }
            };
            
            window.addEventListener('message', messageHandler);
            
            // 发送测试消息
            setTimeout(() => {
                window.postMessage({ type: 'checkPageSupport' }, '*');
                addLog('📤 已发送测试消息');
            }, 500);
            
            // 超时检查
            setTimeout(() => {
                if (!contentScriptResponded) {
                    addCheckResult(
                        '内容脚本响应',
                        false,
                        '内容脚本在3秒内未响应'
                    );
                    addLog('⚠️ 内容脚本未响应');
                    window.removeEventListener('message', messageHandler);
                    
                    // 提供解决建议
                    showSuggestions();
                }
            }, 3000);
        }
        
        function showSuggestions() {
            const suggestionsDiv = document.createElement('div');
            suggestionsDiv.className = 'section';
            suggestionsDiv.innerHTML = `
                <h3>🔧 解决建议</h3>
                <div class="check-item">
                    <strong>1. 检查扩展程序安装</strong>
                    <div>打开 chrome://extensions/ 确认"系统音频录制助手"已安装并启用</div>
                </div>
                <div class="check-item">
                    <strong>2. 启用文件访问权限</strong>
                    <div>在扩展程序详情页面，确保"允许访问文件网址"选项已启用</div>
                </div>
                <div class="check-item">
                    <strong>3. 重新加载扩展程序</strong>
                    <div>在扩展程序页面点击刷新按钮重新加载扩展程序</div>
                </div>
                <div class="check-item">
                    <strong>4. 检查开发者模式</strong>
                    <div>确保Chrome扩展程序页面的"开发者模式"已启用</div>
                </div>
                <div class="check-item">
                    <strong>5. 重新安装扩展程序</strong>
                    <div>卸载现有扩展程序，然后重新安装最新版本</div>
                </div>
            `;
            resultsElement.appendChild(suggestionsDiv);
        }
        
        // 页面加载完成后自动运行诊断
        window.addEventListener('load', () => {
            setTimeout(runDiagnostic, 1000);
        });
    </script>
</body>
</html>
