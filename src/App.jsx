
import './App.css'
import { useState } from 'react'
import MicrophoneRecorder from './components/MicrophoneRecorder'
import ScreenAudioRecorder from './components/ScreenAudioRecorder'
import ScreenRecorderWithAudio from './components/ScreenRecorderWithAudio'
import SystemAudioRecorder from './components/SystemAudioRecorder'
import BrowserCompatibility from './components/BrowserCompatibility'
import AudioTestPage from './components/AudioTestPage'

/**
 * 主应用组件 - 音频录制演示应用
 * <AUTHOR>
 * @created 2025-01-11
 * @description 提供三种音频录制场景的统一界面：麦克风录制、网页音频录制、系统音频录制
 */
function App() {
  const [activeTab, setActiveTab] = useState('microphone')

  /**
   * 切换录制模式标签页
   * @param {string} tab - 标签页名称 ('microphone' | 'screen' | 'system')
   */
  const handleTabChange = (tab) => {
    setActiveTab(tab)
  }

  return (
    <div className="app-container">
      <header className="app-header">
        <h1>音频录制工具</h1>
        <p>支持多种录制场景：麦克风录制、网页音频录制、屏幕录制、系统音频录制</p>
      </header>

      <nav className="tab-navigation">
        <button
          className={`tab-button ${activeTab === 'microphone' ? 'active' : ''}`}
          onClick={() => handleTabChange('microphone')}
        >
          麦克风录制
        </button>
        <button
          className={`tab-button ${activeTab === 'screen' ? 'active' : ''}`}
          onClick={() => handleTabChange('screen')}
        >
          网页音频录制
        </button>
        <button
          className={`tab-button ${activeTab === 'screenVideo' ? 'active' : ''}`}
          onClick={() => handleTabChange('screenVideo')}
        >
          屏幕录制
        </button>
        <button
          className={`tab-button ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => handleTabChange('system')}
        >
          系统音频录制
        </button>
        <button
          className={`tab-button ${activeTab === 'compatibility' ? 'active' : ''}`}
          onClick={() => handleTabChange('compatibility')}
        >
          兼容性检查
        </button>
        <button
          className={`tab-button ${activeTab === 'test' ? 'active' : ''}`}
          onClick={() => handleTabChange('test')}
        >
          音频测试
        </button>
      </nav>

      <main className="tab-content">
        {activeTab === 'microphone' && <MicrophoneRecorder />}
        {activeTab === 'screen' && <ScreenAudioRecorder />}
        {activeTab === 'screenVideo' && <ScreenRecorderWithAudio />}
        {activeTab === 'system' && <SystemAudioRecorder />}
        {activeTab === 'compatibility' && <BrowserCompatibility />}
        {activeTab === 'test' && <AudioTestPage />}
      </main>
    </div>
  )
}

export default App
