import { useReactMediaRecorder } from "react-media-recorder"
import { useState, useRef, useEffect } from 'react'
import AudioQualityDetector from '../utils/AudioQualityDetector'

/**
 * 麦克风音频录制组件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 使用react-media-recorder实现麦克风音频录制功能，支持开始/停止/播放/下载
 */
function MicrophoneRecorder() {
  const [recordingName, setRecordingName] = useState('')
  const [recordings, setRecordings] = useState([])
  const [isSupported, setIsSupported] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')
  const [audioQuality, setAudioQuality] = useState(null)
  const [qualityScore, setQualityScore] = useState(0)
  const audioRef = useRef(null)
  const qualityDetectorRef = useRef(null)

  /**
   * 检查浏览器支持并检测音频质量
   */
  useEffect(() => {
    const checkSupportAndQuality = async () => {
      try {
        // 检查基本的MediaDevices支持
        if (!navigator.mediaDevices) {
          throw new Error('浏览器不支持MediaDevices API')
        }

        // 检查getUserMedia支持
        if (!navigator.mediaDevices.getUserMedia) {
          throw new Error('浏览器不支持getUserMedia API')
        }

        // 检查MediaRecorder支持
        if (!window.MediaRecorder) {
          throw new Error('浏览器不支持MediaRecorder API')
        }

        // 检查是否在安全上下文中（HTTPS或localhost）
        if (!window.isSecureContext) {
          throw new Error('需要在HTTPS环境下运行')
        }

        console.log('✅ 浏览器环境检查通过')
        setIsSupported(true)
        setErrorMessage('')

        // 初始化音频质量检测器
        qualityDetectorRef.current = new AudioQualityDetector()
        
        // 检测最佳音频配置
        console.log('🔍 开始音频质量检测...')
        const optimalConfig = await qualityDetectorRef.current.getOptimalAudioConfig()
        
        setAudioQuality(optimalConfig)
        setQualityScore(optimalConfig.quality.score)
        
        console.log('🎵 音频质量检测完成:')
        console.log(`  质量等级: ${optimalConfig.quality.level}`)
        console.log(`  质量评分: ${optimalConfig.quality.score}/100`)
        console.log(`  最佳配置:`, optimalConfig.quality.specs)

      } catch (error) {
        console.error('❌ 浏览器环境检查失败:', error.message)
        setIsSupported(false)
        setErrorMessage(error.message)
      }
    }

    checkSupportAndQuality()
  }, [])

  /**
   * 获取支持的音频MIME类型 - 优先高质量格式
   * @returns {string} 支持的MIME类型
   */
  const getSupportedAudioMimeType = () => {
    const types = [
      'audio/webm;codecs=opus',    // 最高质量Opus编码
      'audio/ogg;codecs=opus',     // Ogg容器Opus编码
      'audio/webm',                // WebM容器
      'audio/mp4',                 // MP4容器
      'audio/wav'                  // 无损WAV格式
    ]

    console.log('🔍 检查支持的音频格式...')
    for (const type of types) {
      const isSupported = MediaRecorder.isTypeSupported(type)
      console.log(`${type}: ${isSupported ? '✅ 支持' : '❌ 不支持'}`)
      if (isSupported) {
        console.log(`🎵 选择的音频格式: ${type}`)
        return type
      }
    }
    console.log('⚠️ 使用默认格式: audio/webm')
    return 'audio/webm' // 默认值
  }

  // 只在支持的环境中初始化react-media-recorder
  const mediaRecorderConfig = isSupported && audioQuality ? {
    audio: {
      echoCancellation: audioQuality.echoCancellation,
      noiseSuppression: audioQuality.noiseSuppression,
      autoGainControl: audioQuality.autoGainControl,
      sampleRate: audioQuality.sampleRate,
      channelCount: audioQuality.channelCount,
      sampleSize: audioQuality.sampleSize
    },
    video: false,
    mediaRecorderOptions: {
      mimeType: audioQuality.mediaRecorderOptions.mimeType,
      audioBitsPerSecond: audioQuality.mediaRecorderOptions.audioBitsPerSecond
    },
    onStop: (blobUrl, blob) => {
      handleRecordingComplete(blobUrl, blob)
    },
    onStart: () => {
      console.log(`🎵 录制开始 - ${audioQuality.quality.level} 音频模式`)
      console.log(`📊 配置: ${audioQuality.quality.specs.sampleRate}Hz, ${audioQuality.quality.specs.channels}声道, ${audioQuality.quality.specs.bitrate}bps`)
    }
  } : {}

  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    clearBlobUrl,
    muteAudio,
    unmuteAudio,
    isMuted
  } = useReactMediaRecorder(mediaRecorderConfig)

  /**
   * 处理录制完成事件
   * @param {string} blobUrl - 音频blob URL
   * @param {Blob} blob - 音频blob对象
   */
  const handleRecordingComplete = (blobUrl, blob) => {
    const name = recordingName || `录音_${new Date().toLocaleString()}`
    const newRecording = {
      id: Date.now(),
      name,
      url: blobUrl,
      blob,
      duration: 0, // 实际项目中可以计算音频时长
      createdAt: new Date().toISOString()
    }
    
    setRecordings(prev => [...prev, newRecording])
    setRecordingName('')
  }

  /**
   * 开始录制音频
   */
  const handleStartRecording = () => {
    if (status === 'idle') {
      startRecording()
    }
  }

  /**
   * 停止录制音频
   */
  const handleStopRecording = () => {
    if (status === 'recording') {
      stopRecording()
    }
  }

  /**
   * 获取文件扩展名
   * @param {string} mimeType - MIME类型
   * @returns {string} 文件扩展名
   */
  const getFileExtension = (mimeType) => {
    if (mimeType.includes('webm')) return 'webm'
    if (mimeType.includes('mp4')) return 'mp4'
    if (mimeType.includes('ogg')) return 'ogg'
    if (mimeType.includes('wav')) return 'wav'
    return 'webm' // 默认
  }

  /**
   * 下载音频文件
   * @param {Object} recording - 录音对象
   */
  const handleDownload = (recording) => {
    const link = document.createElement('a')
    link.href = recording.url
    const extension = getFileExtension(getSupportedAudioMimeType())
    link.download = `${recording.name}.${extension}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 删除录音
   * @param {number} id - 录音ID
   */
  const handleDelete = (id) => {
    setRecordings(prev => prev.filter(recording => recording.id !== id))
  }

  /**
   * 清除当前录制的音频
   */
  const handleClear = () => {
    clearBlobUrl()
    setRecordingName('')
  }

  /**
   * 获取状态显示文本
   * @returns {string} 状态文本
   */
  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return '准备就绪'
      case 'acquiring_media':
        return '获取麦克风权限中...'
      case 'recording':
        return '正在录制...'
      case 'stopping':
        return '停止录制中...'
      case 'stopped':
        return '录制完成'
      case 'permission_denied':
        return '麦克风权限被拒绝'
      case 'no_specified_media_found':
        return '未找到麦克风设备'
      case 'media_in_use':
        return '麦克风正在被其他应用使用'
      case 'recorder_error':
        return '录制器错误'
      default:
        return status
    }
  }

  // 如果不支持，显示错误信息
  if (!isSupported) {
    return (
      <div className="recorder-container">
        <div className="recorder-header">
          <h2>麦克风音频录制</h2>
          <p>录制来自麦克风的音频，支持实时监听和文件管理</p>
        </div>

        <div className="error-notice">
          <h3>❌ 浏览器环境不支持</h3>
          <p className="error-message">{errorMessage}</p>

          <div className="solutions">
            <h4>💡 解决方案：</h4>
            <ul>
              <li><strong>HTTPS问题：</strong>确保在HTTPS环境下访问（或使用localhost）</li>
              <li><strong>浏览器兼容：</strong>使用Chrome、Firefox或Edge最新版本</li>
              <li><strong>权限设置：</strong>检查浏览器是否允许访问麦克风</li>
              <li><strong>安全上下文：</strong>某些功能需要在安全上下文中运行</li>
            </ul>
          </div>

          <div className="retry-section">
            <button
              className="btn btn-primary"
              onClick={() => window.location.reload()}
            >
              🔄 重新加载页面
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="recorder-container">
      <div className="recorder-header">
        <h2>麦克风音频录制</h2>
        <p>录制来自麦克风的音频，支持实时监听和文件管理</p>
        
        {audioQuality && (
          <div className="quality-indicator">
            <div className="quality-badge">
              <span className="quality-level">{audioQuality.quality.level}</span>
              <span className="quality-score">评分: {qualityScore}/100</span>
            </div>
            <div className="quality-specs">
              <span>🎵 {audioQuality.quality.specs.sampleRate}Hz</span>
              <span>🔊 {audioQuality.quality.specs.channels}声道</span>
              <span>💾 {Math.round(audioQuality.quality.specs.bitrate/1000)}kbps</span>
              <span>📦 {audioQuality.quality.specs.format.split(';')[0]}</span>
            </div>
          </div>
        )}

        <div className="enhancement-notice">
          <h4>🚀 本次更新提升</h4>
          <ul>
            <li><strong>质量检测：</strong>自动检测最佳音频配置，显示质量评分</li>
            <li><strong>格式优化：</strong>优先使用 Opus 编码，支持 320kbps 高比特率</li>
            <li><strong>采样率提升：</strong>从 44.1kHz 提升到 48kHz，音质更清晰</li>
            <li><strong>智能降噪：</strong>启用回声消除、噪声抑制、自动增益控制</li>
            <li><strong>文件质量：</strong>录制文件更大但音质显著提升</li>
          </ul>
        </div>
      </div>

      <div className="recording-controls">
        <div className="status-display">
          <span className={`status-indicator ${status}`}></span>
          <span className="status-text">{getStatusText()}</span>
          {status === 'recording' && (
            <span className="recording-time">⏺️ 录制中</span>
          )}
        </div>

        <div className="input-group">
          <input
            type="text"
            placeholder="输入录音名称（可选）"
            value={recordingName}
            onChange={(e) => setRecordingName(e.target.value)}
            disabled={status === 'recording'}
            className="recording-name-input"
          />
        </div>

        <div className="control-buttons">
          <button
            onClick={handleStartRecording}
            disabled={status !== 'idle'}
            className="btn btn-start"
          >
            🎤 开始录制
          </button>
          
          <button
            onClick={handleStopRecording}
            disabled={status !== 'recording'}
            className="btn btn-stop"
          >
            ⏹️ 停止录制
          </button>

          <button
            onClick={isMuted ? unmuteAudio : muteAudio}
            disabled={status !== 'recording'}
            className="btn btn-mute"
          >
            {isMuted ? '🔇 取消静音' : '🔊 静音'}
          </button>

          <button
            onClick={handleClear}
            disabled={!mediaBlobUrl}
            className="btn btn-clear"
          >
            🗑️ 清除
          </button>
        </div>
      </div>

      {mediaBlobUrl && (
        <div className="audio-preview">
          <h3>当前录制预览</h3>
          <audio
            ref={audioRef}
            src={mediaBlobUrl}
            controls
            className="audio-player"
          />
        </div>
      )}

      <div className="recordings-list">
        <h3>录音列表 ({recordings.length})</h3>
        {recordings.length === 0 ? (
          <p className="empty-message">暂无录音文件</p>
        ) : (
          <div className="recordings-grid">
            {recordings.map((recording) => (
              <div key={recording.id} className="recording-item">
                <div className="recording-info">
                  <h4 className="recording-name">{recording.name}</h4>
                  <p className="recording-date">
                    {new Date(recording.createdAt).toLocaleString()}
                  </p>
                </div>
                <audio
                  src={recording.url}
                  controls
                  className="recording-audio"
                />
                <div className="recording-actions">
                  <button
                    onClick={() => handleDownload(recording)}
                    className="btn btn-download"
                  >
                    📥 下载
                  </button>
                  <button
                    onClick={() => handleDelete(recording.id)}
                    className="btn btn-delete"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default MicrophoneRecorder
