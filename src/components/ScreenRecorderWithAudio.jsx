import { useReactMediaRecorder } from "react-media-recorder"
import { useState, useRef, useEffect } from 'react'
import AudioQualityDetector from '../utils/AudioQualityDetector'

/**
 * 屏幕录制组件（使用react-media-recorder）
 * <AUTHOR>
 * @created 2025-01-11
 * @description 使用react-media-recorder的screen选项录制屏幕和音频
 */
function ScreenRecorderWithAudio() {
  const [recordingName, setRecordingName] = useState('')
  const [recordings, setRecordings] = useState([])
  const [audioQuality, setAudioQuality] = useState(null)
  const [qualityScore, setQualityScore] = useState(0)
  const videoRef = useRef(null)
  const qualityDetectorRef = useRef(null)

  /**
   * 检测音频质量并初始化
   */
  useEffect(() => {
    const detectAudioQuality = async () => {
      try {
        // 初始化音频质量检测器
        qualityDetectorRef.current = new AudioQualityDetector()
        
        // 检测最佳音频配置
        console.log('🔍 屏幕录制 - 开始音频质量检测...')
        const optimalConfig = await qualityDetectorRef.current.getOptimalAudioConfig()
        
        setAudioQuality(optimalConfig)
        setQualityScore(optimalConfig.quality.score)
        
        console.log('🎵 屏幕录制 - 音频质量检测完成:')
        console.log(`  质量等级: ${optimalConfig.quality.level}`)
        console.log(`  质量评分: ${optimalConfig.quality.score}/100`)
        console.log(`  最佳配置:`, optimalConfig.quality.specs)

      } catch (error) {
        console.error('❌ 屏幕录制 - 音频质量检测失败:', error)
      }
    }

    detectAudioQuality()
  }, [])

  /**
   * 获取支持的视频MIME类型（包含音频）- 优先高质量格式
   * @returns {string} 支持的MIME类型
   */
  const getSupportedVideoMimeType = () => {
    const types = [
      'video/webm;codecs=vp9,opus',    // 最高质量：VP9视频 + Opus音频
      'video/webm;codecs=vp8,opus',    // 高质量：VP8视频 + Opus音频  
      'video/mp4;codecs=avc1,opus',    // MP4容器 + Opus音频
      'video/webm',                    // WebM容器
      'video/mp4'                      // MP4容器
    ]
    
    console.log('🔍 屏幕录制 - 检查支持的视频格式...')
    for (const type of types) {
      const isSupported = MediaRecorder.isTypeSupported(type)
      console.log(`${type}: ${isSupported ? '✅ 支持' : '❌ 不支持'}`)
      if (isSupported) {
        console.log(`🎬 选择的视频格式: ${type}`)
        return type
      }
    }
    console.log('⚠️ 使用默认格式: video/webm')
    return 'video/webm' // 默认值
  }

  // 配置高质量的屏幕录制参数
  const screenRecorderConfig = audioQuality ? {
    screen: true,
    audio: {
      echoCancellation: audioQuality.echoCancellation,
      noiseSuppression: audioQuality.noiseSuppression, 
      autoGainControl: audioQuality.autoGainControl,
      sampleRate: audioQuality.sampleRate,
      channelCount: audioQuality.channelCount,
      sampleSize: audioQuality.sampleSize
    },
    video: {
      width: { ideal: 1920 },    // 高清视频
      height: { ideal: 1080 },   // 高清视频
      frameRate: { ideal: 30 }   // 流畅帧率
    },
    mediaRecorderOptions: {
      mimeType: getSupportedVideoMimeType(),
      videoBitsPerSecond: 5000000,  // 5Mbps 视频比特率
      audioBitsPerSecond: audioQuality.mediaRecorderOptions.audioBitsPerSecond
    },
    onStop: (blobUrl, blob) => {
      handleRecordingComplete(blobUrl, blob)
    },
    onStart: () => {
      console.log(`🎬 屏幕录制开始 - ${audioQuality.quality.level} 音频模式`)
      console.log(`📊 配置: ${audioQuality.quality.specs.sampleRate}Hz, ${audioQuality.quality.specs.channels}声道, ${audioQuality.quality.specs.bitrate}bps`)
      console.log(`🎥 视频: 1920x1080@30fps, 5Mbps`)
    }
  } : {
    screen: true,
    audio: true,
    video: true,
    mediaRecorderOptions: {
      mimeType: getSupportedVideoMimeType()
    },
    onStop: (blobUrl, blob) => {
      handleRecordingComplete(blobUrl, blob)
    },
    onStart: () => {
      console.log('屏幕录制开始')
    }
  }

  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    clearBlobUrl,
    previewStream
  } = useReactMediaRecorder(screenRecorderConfig)

  /**
   * 处理录制完成事件
   * @param {string} blobUrl - 视频blob URL
   * @param {Blob} blob - 视频blob对象
   */
  const handleRecordingComplete = (blobUrl, blob) => {
    const name = recordingName || `屏幕录制_${new Date().toLocaleString()}`
    const newRecording = {
      id: Date.now(),
      name,
      url: blobUrl,
      blob,
      createdAt: new Date().toISOString()
    }
    
    setRecordings(prev => [...prev, newRecording])
    setRecordingName('')
  }

  /**
   * 开始录制
   */
  const handleStartRecording = () => {
    if (status === 'idle') {
      startRecording()
    }
  }

  /**
   * 停止录制
   */
  const handleStopRecording = () => {
    if (status === 'recording') {
      stopRecording()
    }
  }

  /**
   * 下载录制文件
   * @param {Object} recording - 录制对象
   */
  const handleDownload = (recording) => {
    const link = document.createElement('a')
    link.href = recording.url
    const extension = getSupportedVideoMimeType().includes('mp4') ? 'mp4' : 'webm'
    link.download = `${recording.name}.${extension}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 删除录制
   * @param {number} id - 录制ID
   */
  const handleDelete = (id) => {
    setRecordings(prev => prev.filter(recording => recording.id !== id))
  }

  /**
   * 清除当前录制
   */
  const handleClear = () => {
    clearBlobUrl()
    setRecordingName('')
  }

  /**
   * 获取状态显示文本
   * @returns {string} 状态文本
   */
  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return '准备就绪'
      case 'acquiring_media':
        return '请选择要录制的屏幕...'
      case 'recording':
        return '正在录制屏幕和音频...'
      case 'stopping':
        return '停止录制中...'
      case 'stopped':
        return '录制完成'
      case 'permission_denied':
        return '屏幕共享权限被拒绝'
      case 'no_specified_media_found':
        return '未找到可录制的媒体'
      case 'media_in_use':
        return '媒体设备正在被使用'
      case 'recorder_error':
        return '录制器错误'
      default:
        return status
    }
  }

  return (
    <div className="recorder-container">
      <div className="recorder-header">
        <h2>屏幕录制（含音频）</h2>
        <p>使用react-media-recorder录制屏幕内容和音频</p>
        
        {audioQuality && (
          <div className="quality-indicator">
            <div className="quality-badge">
              <span className="quality-level">{audioQuality.quality.level}</span>
              <span className="quality-score">评分: {qualityScore}/100</span>
            </div>
            <div className="quality-specs">
              <span>🎵 {audioQuality.quality.specs.sampleRate}Hz</span>
              <span>🔊 {audioQuality.quality.specs.channels}声道</span>
              <span>💾 {Math.round(audioQuality.quality.specs.bitrate/1000)}kbps</span>
              <span>🎥 1920x1080@30fps</span>
              <span>📦 {audioQuality.quality.specs.format.split(';')[0]}</span>
            </div>
          </div>
        )}

        <div className="enhancement-notice">
          <h4>🚀 本次更新提升</h4>
          <ul>
            <li><strong>视频质量：</strong>强制 1080p 分辨率（1920x1080），30fps 流畅帧率</li>
            <li><strong>视频比特率：</strong>提升至 5Mbps，视频更清晰</li>
            <li><strong>音频质量：</strong>48kHz 立体声，320kbps 高比特率</li>
            <li><strong>格式优化：</strong>优先使用 VP9+Opus 编码组合</li>
            <li><strong>质量检测：</strong>自动检测最佳音视频配置</li>
            <li><strong>文件大小：</strong>视频文件更大但质量显著提升</li>
          </ul>
        </div>

        <div className="compatibility-notice">
          <strong>功能说明：</strong>
          <ul>
            <li>📺 录制整个屏幕、应用窗口或浏览器标签页</li>
            <li>🎵 同时录制系统音频（如果支持）</li>
            <li>🎥 输出包含视频和音频的完整录制文件</li>
            <li>⚡ 高质量模式：1080p视频 + 48kHz立体声音频</li>
          </ul>
        </div>
      </div>

      <div className="recording-controls">
        <div className="status-display">
          <span className={`status-indicator ${status}`}></span>
          <span className="status-text">{getStatusText()}</span>
          {status === 'recording' && (
            <span className="recording-time">⏺️ 录制中</span>
          )}
        </div>

        <div className="input-group">
          <input
            type="text"
            placeholder="输入录制名称（可选）"
            value={recordingName}
            onChange={(e) => setRecordingName(e.target.value)}
            disabled={status === 'recording'}
            className="recording-name-input"
          />
        </div>

        <div className="control-buttons">
          <button
            onClick={handleStartRecording}
            disabled={status !== 'idle'}
            className="btn btn-start"
          >
            🖥️ 开始录制
          </button>
          
          <button
            onClick={handleStopRecording}
            disabled={status !== 'recording'}
            className="btn btn-stop"
          >
            ⏹️ 停止录制
          </button>

          <button
            onClick={handleClear}
            disabled={!mediaBlobUrl}
            className="btn btn-clear"
          >
            🗑️ 清除
          </button>
        </div>
      </div>

      {mediaBlobUrl && (
        <div className="video-preview">
          <h3>录制预览</h3>
          <video
            ref={videoRef}
            src={mediaBlobUrl}
            controls
            className="video-player"
            style={{ width: '100%', maxWidth: '800px', height: 'auto' }}
          />
        </div>
      )}

      <div className="instructions">
        <h3>使用说明</h3>
        <ol>
          <li>点击"开始录制"按钮</li>
          <li>在弹出的对话框中选择要录制的内容：
            <ul>
              <li><strong>整个屏幕：</strong>录制完整桌面</li>
              <li><strong>应用程序窗口：</strong>录制特定应用</li>
              <li><strong>浏览器标签页：</strong>录制网页内容</li>
            </ul>
          </li>
          <li>确保勾选"共享音频"选项（如果可用）</li>
          <li>开始播放要录制的音频内容</li>
          <li>点击"停止录制"完成录制</li>
        </ol>
        
        <div className="important-notes">
          <p><strong>音频录制说明：</strong></p>
          <ul>
            <li>🎵 <strong>标签页音频：</strong>选择浏览器标签页时，确保该页面正在播放音频</li>
            <li>🖥️ <strong>系统音频：</strong>选择整个屏幕或应用窗口可能录制到系统音频</li>
            <li>🎤 <strong>麦克风音频：</strong>同时录制麦克风输入（如果权限允许）</li>
            <li>⚠️ <strong>浏览器限制：</strong>不同浏览器对音频录制的支持程度不同</li>
          </ul>
        </div>
      </div>

      <div className="recordings-list">
        <h3>录制列表 ({recordings.length})</h3>
        {recordings.length === 0 ? (
          <p className="empty-message">暂无录制文件</p>
        ) : (
          <div className="recordings-grid">
            {recordings.map((recording) => (
              <div key={recording.id} className="recording-item">
                <div className="recording-info">
                  <h4 className="recording-name">{recording.name}</h4>
                  <p className="recording-date">
                    {new Date(recording.createdAt).toLocaleString()}
                  </p>
                </div>
                <video
                  src={recording.url}
                  controls
                  className="recording-video"
                  style={{ width: '100%', height: '200px', objectFit: 'contain' }}
                />
                <div className="recording-actions">
                  <button
                    onClick={() => handleDownload(recording)}
                    className="btn btn-download"
                  >
                    📥 下载
                  </button>
                  <button
                    onClick={() => handleDelete(recording.id)}
                    className="btn btn-delete"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ScreenRecorderWithAudio
