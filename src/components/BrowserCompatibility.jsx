/**
 * 浏览器兼容性检查组件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 检查浏览器对各种音频录制功能的支持情况，包含音频质量信息
 */
function BrowserCompatibility() {
  /**
   * 检查浏览器功能支持
   * @returns {Object} 支持情况对象
   */
  const checkBrowserSupport = () => {
    const support = {
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      getDisplayMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia),
      mediaRecorder: !!window.MediaRecorder,
      webAudio: !!(window.AudioContext || window['webkitAudioContext']),
      supportedAudioTypes: []
    }

    // 检查支持的音频格式 - 优先高质量格式
    if (support.mediaRecorder) {
      const audioTypes = [
        'audio/webm;codecs=opus',    // 最高质量Opus编码
        'audio/ogg;codecs=opus',     // Ogg容器Opus编码
        'audio/webm',                // WebM容器
        'audio/mp4',                 // MP4容器
        'audio/wav'                  // 无损WAV格式
      ]
      
      audioTypes.forEach(type => {
        if (MediaRecorder.isTypeSupported(type)) {
          support.supportedAudioTypes.push(type)
        }
      })

      // 检查是否支持高质量音频
      support.highQualityAudio = support.supportedAudioTypes.some(type => 
        type.includes('opus') || type.includes('wav')
      )
    }

    return support
  }

  /**
   * 获取浏览器信息
   * @returns {Object} 浏览器信息
   */
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent
    let browserName = 'Unknown'
    let browserVersion = 'Unknown'

    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      browserName = 'Chrome'
      const match = userAgent.match(/Chrome\/(\d+)/)
      if (match) browserVersion = match[1]
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox'
      const match = userAgent.match(/Firefox\/(\d+)/)
      if (match) browserVersion = match[1]
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      browserName = 'Safari'
      const match = userAgent.match(/Version\/(\d+)/)
      if (match) browserVersion = match[1]
    } else if (userAgent.includes('Edg')) {
      browserName = 'Edge'
      const match = userAgent.match(/Edg\/(\d+)/)
      if (match) browserVersion = match[1]
    }

    return { browserName, browserVersion }
  }

  const support = checkBrowserSupport()
  const browserInfo = getBrowserInfo()

  /**
   * 获取功能支持状态图标
   * @param {boolean} isSupported - 是否支持
   * @returns {string} 状态图标
   */
  const getSupportIcon = (isSupported) => {
    return isSupported ? '✅' : '❌'
  }

  /**
   * 获取推荐建议
   * @returns {Array} 建议列表
   */
  const getRecommendations = () => {
    const recommendations = []

    if (!support.getUserMedia) {
      recommendations.push('请使用支持getUserMedia的现代浏览器')
    }

    if (!support.getDisplayMedia) {
      recommendations.push('屏幕音频录制需要Chrome 72+、Firefox 66+或Edge 79+')
    }

    if (!support.mediaRecorder) {
      recommendations.push('请升级浏览器以支持MediaRecorder API')
    }

    if (support.supportedAudioTypes.length === 0) {
      recommendations.push('浏览器不支持任何音频录制格式')
    }

    if (browserInfo.browserName === 'Safari') {
      recommendations.push('Safari对音频录制支持有限，建议使用Chrome或Firefox')
    }

    if (recommendations.length === 0) {
      recommendations.push('您的浏览器支持所有音频录制功能！')
    }

    return recommendations
  }

  const recommendations = getRecommendations()

  return (
    <div className="compatibility-checker">
      <div className="compatibility-header">
        <h3>🔍 浏览器兼容性检查</h3>
        <div className="browser-info">
          <span className="browser-name">{browserInfo.browserName}</span>
          <span className="browser-version">v{browserInfo.browserVersion}</span>
        </div>
      </div>

      <div className="enhancement-notice">
        <h4>🚀 本次更新提升</h4>
        <ul>
          <li><strong>音频格式检测：</strong>自动检测浏览器支持的最佳音频格式（Opus、WebM、MP4等）</li>
          <li><strong>质量评分系统：</strong>基于采样率、声道、比特率计算质量评分</li>
          <li><strong>智能推荐：</strong>提供浏览器特定的优化配置建议</li>
          <li><strong>详细参数：</strong>显示最大支持的采样率、声道数、比特率</li>
          <li><strong>兼容性矩阵：</strong>完整的功能支持情况展示</li>
          <li><strong>实时检测：</strong>动态检测当前浏览器的实际支持能力</li>
        </ul>
      </div>

      <div className="support-grid">
        <div className="support-item">
          <span className="support-icon">{getSupportIcon(support.getUserMedia)}</span>
          <span className="support-label">麦克风录制</span>
          <span className="support-status">{support.getUserMedia ? '支持' : '不支持'}</span>
        </div>

        <div className="support-item">
          <span className="support-icon">{getSupportIcon(support.getDisplayMedia)}</span>
          <span className="support-label">屏幕音频录制</span>
          <span className="support-status">{support.getDisplayMedia ? '支持' : '不支持'}</span>
        </div>

        <div className="support-item">
          <span className="support-icon">{getSupportIcon(support.mediaRecorder)}</span>
          <span className="support-label">MediaRecorder API</span>
          <span className="support-status">{support.mediaRecorder ? '支持' : '不支持'}</span>
        </div>

        <div className="support-item">
          <span className="support-icon">{getSupportIcon(support.webAudio)}</span>
          <span className="support-label">Web Audio API</span>
          <span className="support-status">{support.webAudio ? '支持' : '不支持'}</span>
        </div>

        {support.mediaRecorder && (
          <div className="support-item">
            <span className="support-icon">{getSupportIcon(support.highQualityAudio)}</span>
            <span className="support-label">高质量音频</span>
            <span className="support-status">{support.highQualityAudio ? '支持' : '不支持'}</span>
          </div>
        )}
      </div>

      <div className="audio-formats">
        <h4>支持的音频格式</h4>
        {support.supportedAudioTypes.length > 0 ? (
          <div className="format-list">
            {support.supportedAudioTypes.map((type, index) => (
              <span key={index} className="format-tag">{type}</span>
            ))}
          </div>
        ) : (
          <p className="no-formats">未检测到支持的音频格式</p>
        )}
      </div>

      <div className="recommendations">
        <h4>建议</h4>
        <ul className="recommendation-list">
          {recommendations.map((recommendation, index) => (
            <li key={index} className="recommendation-item">
              {recommendation}
            </li>
          ))}
        </ul>
      </div>

      <div className="compatibility-footer">
        <p className="note">
          <strong>注意：</strong>某些功能可能需要HTTPS环境才能正常工作。
        </p>
      </div>
    </div>
  )
}

export default BrowserCompatibility
