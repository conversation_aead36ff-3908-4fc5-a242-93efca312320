import { useState, useRef } from 'react'

/**
 * 网页音频录制组件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 使用getDisplayMedia API录制其他网页正在播放的音频内容
 */
function ScreenAudioRecorder() {
  const [isRecording, setIsRecording] = useState(false)
  const [recordings, setRecordings] = useState([])
  const [recordingName, setRecordingName] = useState('')
  const [status, setStatus] = useState('idle')
  const [currentRecordingUrl, setCurrentRecordingUrl] = useState('')
  
  const mediaRecorderRef = useRef(null)
  const streamRef = useRef(null)
  const chunksRef = useRef([])

  /**
   * 开始录制屏幕音频
   */
  const startScreenAudioRecording = async () => {
    try {
      setStatus('acquiring_media')

      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        throw new Error('浏览器不支持屏幕共享功能')
      }

      console.log('开始请求屏幕共享权限...')

      // 请求屏幕共享权限，必须包含视频但我们只使用音频
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          mediaSource: 'tab' // 优先选择标签页
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          channelCount: 2,
          sampleSize: 16
        }
      })

      console.log('获取到媒体流:', stream)
      console.log('音频轨道数量:', stream.getAudioTracks().length)
      console.log('视频轨道数量:', stream.getVideoTracks().length)

      // 检查是否有音频轨道
      const hasAudio = stream.getAudioTracks().length > 0
      if (!hasAudio) {
        console.warn('❌ 所选内容没有音频轨道')
        console.log('💡 解决方案：')
        console.log('1. 确保目标页面正在播放音频')
        console.log('2. 重新选择并勾选"共享音频"选项')
        console.log('3. 尝试选择"整个屏幕"而不是单个标签页')
        console.log('4. 先到"音频测试"页面播放测试音频')

        stream.getTracks().forEach(track => track.stop())
        setStatus('no_audio_track')
        return
      }

      console.log('✅ 检测到音频轨道，准备开始录制')

      streamRef.current = stream // 保存原始流用于停止
      chunksRef.current = []

      // 创建只包含音频轨道的流
      const audioTracks = stream.getAudioTracks()
      const audioOnlyStream = new MediaStream(audioTracks)

      // 检查浏览器支持的音频格式 - 优先高质量格式
      let mediaRecorderOptions = {}
      const supportedAudioTypes = [
        'audio/webm;codecs=opus',    // 最高质量Opus编码
        'audio/ogg;codecs=opus',     // Ogg容器Opus编码
        'audio/webm',                // WebM容器
        'audio/mp4',                 // MP4容器
        'audio/wav'                  // 无损WAV格式
      ]

      console.log('🔍 检查支持的音频格式...')
      for (const type of supportedAudioTypes) {
        const isSupported = MediaRecorder.isTypeSupported(type)
        console.log(`${type}: ${isSupported ? '✅ 支持' : '❌ 不支持'}`)
        if (isSupported) {
          mediaRecorderOptions.mimeType = type
          mediaRecorderOptions.audioBitsPerSecond = 320000  // 设置高比特率
          console.log(`🎵 选择的音频格式: ${type} (320kbps)`)
          break
        }
      }

      const mediaRecorder = new MediaRecorder(audioOnlyStream, mediaRecorderOptions)
      console.log('✅ MediaRecorder创建成功，专用于音频录制')

      mediaRecorderRef.current = mediaRecorder

      // 监听数据可用事件
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }

      // 监听录制停止事件
      mediaRecorder.onstop = () => {
        handleRecordingComplete()
      }

      // 监听流结束事件（用户停止共享）
      stream.getTracks().forEach(track => {
        track.onended = () => {
          if (isRecording) {
            stopScreenAudioRecording()
          }
        }
      })

      // 开始录制
      mediaRecorder.start(1000) // 每秒收集一次数据
      setIsRecording(true)
      setStatus('recording')

    } catch (error) {
      console.error('开始录制失败:', error)
      console.error('错误名称:', error.name)
      console.error('错误消息:', error.message)

      if (error.name === 'NotAllowedError') {
        setStatus('permission_denied')
      } else if (error.name === 'NotFoundError') {
        setStatus('no_audio_track')
      } else if (error.name === 'NotSupportedError') {
        setStatus('not_supported')
      } else if (error.message.includes('没有音频轨道')) {
        setStatus('no_audio_track')
      } else if (error.message.includes('不支持屏幕共享')) {
        setStatus('not_supported')
      } else {
        setStatus('recorder_error')
      }
    }
  }

  /**
   * 停止录制屏幕音频
   */
  const stopScreenAudioRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      setStatus('stopping')
      mediaRecorderRef.current.stop()

      // 停止所有轨道（包括视频和音频）
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => {
          track.stop()
        })
      }

      setIsRecording(false)
    }
  }

  /**
   * 处理录制完成
   */
  const handleRecordingComplete = () => {
    // 创建音频blob
    const blob = new Blob(chunksRef.current, { type: 'audio/webm' })
    const url = URL.createObjectURL(blob)

    setCurrentRecordingUrl(url)
    setStatus('stopped')

    // 添加到录音列表
    const name = recordingName || `网页音频_${new Date().toLocaleString()}`
    const newRecording = {
      id: Date.now(),
      name,
      url,
      blob,
      createdAt: new Date().toISOString()
    }

    setRecordings(prev => [...prev, newRecording])
    setRecordingName('')
    chunksRef.current = []

    console.log('✅ 音频录制完成:', name)
  }

  /**
   * 下载音频文件
   * @param {Object} recording - 录音对象
   */
  const handleDownload = (recording) => {
    const link = document.createElement('a')
    link.href = recording.url
    link.download = `${recording.name}.webm` // 音频文件
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    console.log('📥 下载音频文件:', recording.name)
  }

  /**
   * 删除录音
   * @param {number} id - 录音ID
   */
  const handleDelete = (id) => {
    setRecordings(prev => prev.filter(recording => recording.id !== id))
  }

  /**
   * 清除当前录制
   */
  const handleClear = () => {
    if (currentRecordingUrl) {
      URL.revokeObjectURL(currentRecordingUrl)
      setCurrentRecordingUrl('')
    }
    setStatus('idle')
  }

  /**
   * 获取状态显示文本
   * @returns {string} 状态文本
   */
  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return '准备就绪 - 等待开始音频录制'
      case 'acquiring_media':
        return '请选择要录制音频的标签页或窗口...'
      case 'recording':
        return '🎵 正在录制网页音频...'
      case 'stopping':
        return '停止录制中...'
      case 'stopped':
        return '✅ 音频录制完成'
      case 'permission_denied':
        return '❌ 屏幕共享权限被拒绝'
      case 'no_audio_track':
        return '❌ 所选内容没有音频轨道，请选择正在播放音频的页面'
      case 'not_supported':
        return '❌ 浏览器不支持此功能'
      case 'recorder_error':
        return '❌ 录制器错误'
      default:
        return status
    }
  }

  return (
    <div className="recorder-container">
      <div className="recorder-header">
        <h2>网页音频录制</h2>
        <p>专门录制其他网页正在播放的音频内容，只输出音频文件</p>

        <div className="enhancement-notice">
          <h4>🚀 本次更新提升</h4>
          <ul>
            <li><strong>音频质量：</strong>48kHz 立体声，320kbps 高比特率</li>
            <li><strong>格式优化：</strong>优先使用 Opus 编码，音质更清晰</li>
            <li><strong>智能降噪：</strong>启用回声消除、噪声抑制、自动增益控制</li>
            <li><strong>采样深度：</strong>16bit 采样深度，保留更多音频细节</li>
            <li><strong>格式检测：</strong>自动选择浏览器支持的最佳音频格式</li>
            <li><strong>文件质量：</strong>音频文件更大但音质显著提升</li>
          </ul>
        </div>

        <div className="audio-only-notice">
          <h3>🎵 纯音频录制说明</h3>
          <p>此功能专门用于录制音频，不会保存视频内容。即使浏览器要求选择视频源，我们也只提取和保存音频部分。</p>
        </div>

        <div className="compatibility-notice">
          <strong>浏览器兼容性：</strong>
          <ul>
            <li>✅ Chrome/Edge: 完全支持音频捕获</li>
            <li>⚠️ Firefox: 部分支持，可能需要特殊设置</li>
            <li>❌ Safari: 不支持标签页音频捕获</li>
          </ul>
        </div>

        <div className="important-tips">
          <h4>💡 重要提示</h4>
          <ul>
            <li>确保目标网页正在播放音频</li>
            <li>在屏幕共享对话框中务必勾选"共享音频"选项</li>
            <li>如果没有音频选项，尝试选择"整个屏幕"而不是单个标签页</li>
            <li>建议先在"音频测试"页面播放测试音频进行验证</li>
          </ul>
        </div>
      </div>

      <div className="recording-controls">
        <div className="status-display">
          <span className={`status-indicator ${status}`}></span>
          <span className="status-text">{getStatusText()}</span>
          {isRecording && (
            <span className="recording-time">⏺️ 录制中</span>
          )}
        </div>

        <div className="input-group">
          <input
            type="text"
            placeholder="输入录音名称（可选）"
            value={recordingName}
            onChange={(e) => setRecordingName(e.target.value)}
            disabled={isRecording}
            className="recording-name-input"
          />
        </div>

        <div className="control-buttons">
          <button
            onClick={startScreenAudioRecording}
            disabled={isRecording || status === 'acquiring_media'}
            className="btn btn-start"
          >
            🖥️ 开始录制
          </button>
          
          <button
            onClick={stopScreenAudioRecording}
            disabled={!isRecording}
            className="btn btn-stop"
          >
            ⏹️ 停止录制
          </button>

          <button
            onClick={handleClear}
            disabled={!currentRecordingUrl}
            className="btn btn-clear"
          >
            🗑️ 清除
          </button>
        </div>
      </div>

      {currentRecordingUrl && (
        <div className="audio-preview">
          <h3>当前录制预览</h3>
          {streamRef.current && streamRef.current.getAudioTracks().length > 0 ? (
            <audio
              src={currentRecordingUrl}
              controls
              className="audio-player"
            />
          ) : (
            <video
              src={currentRecordingUrl}
              controls
              className="video-player"
              style={{ width: '100%', maxWidth: '600px', height: 'auto' }}
            />
          )}
        </div>
      )}

      <div className="instructions">
        <h3>📋 详细使用说明</h3>
        <div className="step-by-step">
          <div className="step">
            <span className="step-number">1</span>
            <div className="step-content">
              <h4>准备音频源</h4>
              <p>确保要录制的网页正在播放音频。建议先到"音频测试"页面播放测试音频。</p>
            </div>
          </div>

          <div className="step">
            <span className="step-number">2</span>
            <div className="step-content">
              <h4>开始录制</h4>
              <p>点击"开始录制"按钮，浏览器会弹出屏幕共享对话框。</p>
            </div>
          </div>

          <div className="step">
            <span className="step-number">3</span>
            <div className="step-content">
              <h4>选择音频源</h4>
              <p><strong>关键步骤：</strong>在对话框中选择正在播放音频的标签页，并<strong>务必勾选"共享音频"选项</strong>。</p>
            </div>
          </div>

          <div className="step">
            <span className="step-number">4</span>
            <div className="step-content">
              <h4>完成录制</h4>
              <p>录制开始后，点击"停止录制"完成。录制的音频文件会自动保存。</p>
            </div>
          </div>
        </div>
      </div>

      {status === 'no_audio_track' && (
        <div className="troubleshooting-guide">
          <h3>🔧 没有音频轨道？试试这些解决方案</h3>

          <div className="solution-cards">
            <div className="solution-card urgent">
              <h4>🎵 方案1：使用音频测试页面</h4>
              <p>先到"音频测试"标签页播放测试音频，然后重新录制。</p>
              <button
                className="btn btn-primary"
                onClick={() => window.location.hash = '#test'}
              >
                前往音频测试页面
              </button>
            </div>

            <div className="solution-card">
              <h4>🖥️ 方案2：选择整个屏幕</h4>
              <p>在屏幕共享对话框中选择"整个屏幕"而不是单个标签页。</p>
              <ul>
                <li>重新点击"开始录制"</li>
                <li>选择"整个屏幕"选项</li>
                <li>确保勾选"共享音频"</li>
              </ul>
            </div>

            <div className="solution-card">
              <h4>🌐 方案3：检查浏览器设置</h4>
              <p>确保浏览器支持音频捕获功能。</p>
              <ul>
                <li>使用Chrome或Edge浏览器</li>
                <li>确保浏览器版本是最新的</li>
                <li>检查是否在HTTPS环境下</li>
              </ul>
            </div>

            <div className="solution-card">
              <h4>🎧 方案4：使用其他录制方式</h4>
              <p>如果网页音频录制不工作，尝试其他方式。</p>
              <ul>
                <li>使用"屏幕录制"功能（包含视频）</li>
                <li>使用"麦克风录制"录制扬声器输出</li>
                <li>使用专门的录音软件</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      <div className="browser-specific-tips">
        <h3>💡 浏览器特定提示</h3>
        <div className="browser-tips-grid">
          <div className="browser-tip chrome">
            <h4>Chrome/Edge</h4>
            <div className="tip-content">
              <p><strong>✅ 推荐使用</strong></p>
              <ul>
                <li>完全支持标签页音频捕获</li>
                <li>在对话框中选择"Chrome标签页"</li>
                <li>确保勾选底部的"共享音频"复选框</li>
              </ul>
            </div>
          </div>

          <div className="browser-tip firefox">
            <h4>Firefox</h4>
            <div className="tip-content">
              <p><strong>⚠️ 部分支持</strong></p>
              <ul>
                <li>可能需要在about:config中启用设置</li>
                <li>建议选择"整个屏幕"而不是标签页</li>
                <li>如果不工作，建议使用Chrome</li>
              </ul>
            </div>
          </div>

          <div className="browser-tip safari">
            <h4>Safari</h4>
            <div className="tip-content">
              <p><strong>❌ 不支持</strong></p>
              <ul>
                <li>Safari不支持标签页音频捕获</li>
                <li>建议使用Chrome或Firefox</li>
                <li>可以尝试录制整个屏幕</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="recordings-list">
        <h3>录音列表 ({recordings.length})</h3>
        {recordings.length === 0 ? (
          <p className="empty-message">暂无录音文件</p>
        ) : (
          <div className="recordings-grid">
            {recordings.map((recording) => (
              <div key={recording.id} className="recording-item">
                <div className="recording-info">
                  <h4 className="recording-name">{recording.name}</h4>
                  <p className="recording-date">
                    {new Date(recording.createdAt).toLocaleString()}
                  </p>
                </div>
                {recording.hasAudio ? (
                  <audio
                    src={recording.url}
                    controls
                    className="recording-audio"
                  />
                ) : (
                  <video
                    src={recording.url}
                    controls
                    className="recording-video"
                    style={{ width: '100%', height: '200px', objectFit: 'contain' }}
                  />
                )}
                <div className="recording-actions">
                  <button
                    onClick={() => handleDownload(recording)}
                    className="btn btn-download"
                  >
                    📥 下载
                  </button>
                  <button
                    onClick={() => handleDelete(recording.id)}
                    className="btn btn-delete"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ScreenAudioRecorder
