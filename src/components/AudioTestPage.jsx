import { useState, useRef, useEffect } from 'react'

/**
 * 音频测试页面组件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 提供一个有音频播放的测试页面，用于测试屏幕音频录制功能
 */
function AudioTestPage() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState(0)
  const audioRef = useRef(null)
  const audioContextRef = useRef(null)
  const oscillatorRef = useRef(null)
  const gainNodeRef = useRef(null)
  const stereoPannerRef = useRef(null)

  // 测试音频列表
  const testAudios = [
    {
      name: '测试音频 1 - 440Hz正弦波',
      url: null, // 将通过Web Audio API生成
      description: '使用Web Audio API生成的440Hz正弦波'
    },
    {
      name: '测试音频 2 - 多频率混合',
      url: null, // 将通过Web Audio API生成
      description: '混合多个频率的测试音调'
    }
  ]

  /**
   * 播放/暂停音频
   */
  const togglePlayback = () => {
    if (currentTrack === 0 || currentTrack === 1) {
      // 使用Web Audio API生成音频
      if (isPlaying) {
        stopGeneratedAudio()
      } else {
        playGeneratedAudio(currentTrack)
      }
    } else if (audioRef.current) {
      // 使用HTML5 audio元素
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
    }
  }

  /**
   * 播放生成的高质量音频
   * @param {number} trackIndex - 音频轨道索引
   */
  const playGeneratedAudio = (trackIndex) => {
    try {
      // 创建高质量音频上下文
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 48000,
        latencyHint: 'interactive'
      })

      oscillatorRef.current = audioContextRef.current.createOscillator()
      gainNodeRef.current = audioContextRef.current.createGain()
      stereoPannerRef.current = audioContextRef.current.createStereoPanner()

      // 连接音频处理链
      oscillatorRef.current.connect(gainNodeRef.current)
      gainNodeRef.current.connect(stereoPannerRef.current)
      stereoPannerRef.current.connect(audioContextRef.current.destination)

      // 根据轨道设置不同的音频参数
      if (trackIndex === 0) {
        // 440Hz 正弦波 - 高质量
        oscillatorRef.current.frequency.setValueAtTime(440, audioContextRef.current.currentTime)
        oscillatorRef.current.type = 'sine'
        stereoPannerRef.current.pan.setValueAtTime(0, audioContextRef.current.currentTime) // 居中
      } else if (trackIndex === 1) {
        // 多频率混合 - 更复杂的音频
        oscillatorRef.current.frequency.setValueAtTime(880, audioContextRef.current.currentTime) // A5音符
        oscillatorRef.current.type = 'triangle'
        stereoPannerRef.current.pan.setValueAtTime(0, audioContextRef.current.currentTime)
        
        // 添加频率调制以创建更丰富的音频
        oscillatorRef.current.frequency.exponentialRampToValueAtTime(440, audioContextRef.current.currentTime + 1)
        oscillatorRef.current.frequency.exponentialRampToValueAtTime(880, audioContextRef.current.currentTime + 2)
      }

      // 高质量音量控制
      gainNodeRef.current.gain.setValueAtTime(0.3, audioContextRef.current.currentTime)
      gainNodeRef.current.gain.exponentialRampToValueAtTime(0.2, audioContextRef.current.currentTime + 0.1)

      // 开始播放
      oscillatorRef.current.start()
      setIsPlaying(true)
      console.log(`🎵 开始播放高质量音频 (48kHz, 立体声) - 轨道 ${trackIndex + 1}`)

    } catch (error) {
      console.error('播放生成音频失败:', error)
    }
  }

  /**
   * 停止生成的音频
   */
  const stopGeneratedAudio = () => {
    if (oscillatorRef.current) {
      // 淡出效果
      if (gainNodeRef.current && audioContextRef.current) {
        gainNodeRef.current.gain.exponentialRampToValueAtTime(0.01, audioContextRef.current.currentTime + 0.1)
        oscillatorRef.current.stop(audioContextRef.current.currentTime + 0.1)
      } else {
        oscillatorRef.current.stop()
      }
      oscillatorRef.current = null
    }

    if (audioContextRef.current) {
      audioContextRef.current.close().catch(() => {})
      audioContextRef.current = null
    }

    gainNodeRef.current = null
    stereoPannerRef.current = null
    setIsPlaying(false)
    console.log('⏹️ 停止播放生成音频')
  }

  /**
   * 切换音频轨道
   * @param {number} index - 音频索引
   */
  const switchTrack = (index) => {
    setCurrentTrack(index)
    setIsPlaying(false)
    if (audioRef.current) {
      audioRef.current.pause()
    }
  }

  /**
   * 生成测试音频（使用Web Audio API - 高质量音频）
   */
  const generateTestTone = () => {
    try {
      // 创建高质量音频上下文
      const audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 48000,  // 专业音频采样率
        latencyHint: 'interactive'
      })
      
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      const stereoPanner = audioContext.createStereoPanner()

      // 连接音频处理链
      oscillator.connect(gainNode)
      gainNode.connect(stereoPanner)
      stereoPanner.connect(audioContext.destination)

      // 设置高质量音频参数
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime) // A4音符
      oscillator.type = 'sine'  // 纯正弦波，最高质量
      
      // 立体声设置
      stereoPanner.pan.setValueAtTime(0, audioContext.currentTime) // 居中

      // 高质量音量控制
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 2)

      console.log('🎵 生成高质量测试音频 (48kHz, 立体声)')
      return true
    } catch (error) {
      console.error('生成测试音频失败:', error)
      return false
    }
  }

  /**
   * 处理音频播放事件
   */
  useEffect(() => {
    const audio = audioRef.current
    if (audio) {
      const handlePlay = () => setIsPlaying(true)
      const handlePause = () => setIsPlaying(false)
      const handleEnded = () => setIsPlaying(false)

      audio.addEventListener('play', handlePlay)
      audio.addEventListener('pause', handlePause)
      audio.addEventListener('ended', handleEnded)

      return () => {
        audio.removeEventListener('play', handlePlay)
        audio.removeEventListener('pause', handlePause)
        audio.removeEventListener('ended', handleEnded)
      }
    }
  }, [currentTrack])

  return (
    <div className="audio-test-page">
      <div className="test-header">
        <h2>🎵 高质量音频测试页面</h2>
        <p>这个页面提供高质量音频播放（48kHz 立体声），用于测试屏幕音频录制功能</p>
        <div className="quality-badge">
          <span className="quality-indicator">🎧 高质量音频</span>
          <span className="quality-specs">48kHz • 立体声 • 320kbps</span>
        </div>

        <div className="enhancement-notice">
          <h4>🚀 本次更新提升</h4>
          <ul>
            <li><strong>音频质量：</strong>使用 Web Audio API 生成 48kHz 高质量测试音频</li>
            <li><strong>立体声支持：</strong>完整的立体声输出，支持声像控制</li>
            <li><strong>测试音调：</strong>提供 440Hz 正弦波和多频率混合测试音</li>
            <li><strong>实时生成：</strong>动态生成测试音频，无需外部音频文件</li>
            <li><strong>音质控制：</strong>精确的音量控制和淡入淡出效果</li>
            <li><strong>状态显示：</strong>实时显示音频播放状态和技术参数</li>
          </ul>
        </div>
      </div>

      <div className="test-instructions">
        <h3>📋 使用说明</h3>
        <ol>
          <li><strong>播放音频：</strong>点击下方的播放按钮开始播放测试音频</li>
          <li><strong>打开录制页面：</strong>在新标签页中打开"网页音频录制"或"屏幕录制"功能</li>
          <li><strong>选择此标签页：</strong>在屏幕共享对话框中选择这个正在播放音频的标签页</li>
          <li><strong>勾选音频选项：</strong>确保勾选"共享音频"或"包含音频"选项</li>
          <li><strong>开始录制：</strong>开始录制，应该能够捕获到音频</li>
        </ol>
      </div>

      <div className="audio-controls">
        <h3>🎛️ 音频控制</h3>
        
        <div className="track-selector">
          <h4>选择测试音频：</h4>
          <div className="track-buttons">
            {testAudios.map((track, index) => (
              <button
                key={index}
                onClick={() => switchTrack(index)}
                className={`btn ${currentTrack === index ? 'btn-primary' : 'btn-secondary'}`}
              >
                {track.name}
              </button>
            ))}
          </div>
        </div>

        <div className="playback-controls">
          <audio
            ref={audioRef}
            src={testAudios[currentTrack].url}
            loop
            preload="auto"
          />
          
          <button
            onClick={togglePlayback}
            className={`btn ${isPlaying ? 'btn-stop' : 'btn-start'}`}
          >
            {isPlaying ? '⏸️ 暂停播放' : '▶️ 开始播放'}
          </button>

          <button
            onClick={generateTestTone}
            className="btn btn-secondary"
          >
            🎵 生成测试音调
          </button>
        </div>

        <div className="current-track-info">
          <h4>当前播放：</h4>
          <p><strong>{testAudios[currentTrack].name}</strong></p>
          <p>{testAudios[currentTrack].description}</p>
          <div className="playback-status">
            状态: <span className={isPlaying ? 'status-playing' : 'status-stopped'}>
              {isPlaying ? '🔊 播放中' : '⏹️ 已停止'}
            </span>
          </div>
        </div>
      </div>

      <div className="browser-tips">
        <h3>💡 浏览器提示</h3>
        <div className="tips-grid">
          <div className="tip-item">
            <h4>Chrome/Edge</h4>
            <ul>
              <li>✅ 支持标签页音频捕获</li>
              <li>在屏幕共享对话框中选择"Chrome标签页"</li>
              <li>确保勾选"共享音频"选项</li>
            </ul>
          </div>
          
          <div className="tip-item">
            <h4>Firefox</h4>
            <ul>
              <li>⚠️ 部分支持音频捕获</li>
              <li>可能需要在about:config中启用相关设置</li>
              <li>建议使用Chrome进行测试</li>
            </ul>
          </div>
          
          <div className="tip-item">
            <h4>Safari</h4>
            <ul>
              <li>❌ 不支持标签页音频捕获</li>
              <li>建议使用Chrome或Firefox</li>
              <li>可以尝试录制整个屏幕</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="troubleshooting">
        <h3>🔧 故障排除</h3>
        <div className="troubleshooting-list">
          <div className="issue">
            <h4>❌ 录制没有声音</h4>
            <ul>
              <li>确保此页面正在播放音频</li>
              <li>检查是否勾选了"共享音频"选项</li>
              <li>尝试选择"整个屏幕"而不是单个标签页</li>
              <li>检查系统音量设置</li>
            </ul>
          </div>
          
          <div className="issue">
            <h4>⚠️ 无法选择音频选项</h4>
            <ul>
              <li>浏览器可能不支持音频捕获</li>
              <li>尝试更新浏览器到最新版本</li>
              <li>使用Chrome浏览器进行测试</li>
            </ul>
          </div>
          
          <div className="issue">
            <h4>🚫 屏幕共享被拒绝</h4>
            <ul>
              <li>检查浏览器权限设置</li>
              <li>确保在HTTPS环境下运行</li>
              <li>重新加载页面并重试</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="test-footer">
        <p><strong>提示：</strong>保持此页面在前台播放音频，然后在其他标签页中进行录制测试。</p>
      </div>
    </div>
  )
}

export default AudioTestPage
