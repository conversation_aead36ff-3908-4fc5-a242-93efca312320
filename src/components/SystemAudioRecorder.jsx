import { useState, useEffect, useRef } from 'react'

/**
 * 系统音频录制组件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 集成Chrome扩展程序实现系统音频录制功能
 */
function SystemAudioRecorder() {
  // 状态管理
  const [extensionStatus, setExtensionStatus] = useState('checking') // checking, available, unavailable
  const [isRecording, setIsRecording] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioChunks, setAudioChunks] = useState([])
  const [currentAudioUrl, setCurrentAudioUrl] = useState('')
  const [selectedSource, setSelectedSource] = useState('tab') // tab, desktop
  const [recordingName, setRecordingName] = useState('')
  const [statusMessage, setStatusMessage] = useState('')
  const [pageSupport, setPageSupport] = useState(null)
  
  // 引用
  const recordingTimerRef = useRef(null)
  const recordingStartTimeRef = useRef(null)
  const audioContextRef = useRef(null)
  const mediaRecorderRef = useRef(null)
  
  // 扩展程序检测 - 使用内容脚本通信方式

  /**
   * 检查扩展程序是否可用
   */
  const checkExtensionAvailability = () => {
    try {
      console.log('🔍 开始检查扩展程序可用性...')
      
      // 检查是否在Chrome浏览器中
      if (!window.chrome || !window.chrome.runtime) {
        setExtensionStatus('unavailable')
        setStatusMessage('请使用Chrome浏览器以获得最佳体验')
        console.log('❌ 不在Chrome浏览器中')
        return
      }
      
      console.log('✅ Chrome浏览器检测通过')
      console.log('🔧 Chrome Runtime:', window.chrome.runtime)
      
      // 设置超时检查
      const timeoutId = setTimeout(() => {
        if (extensionStatus === 'checking') {
          console.log('⏰ 扩展程序检测超时')
          setExtensionStatus('unavailable')
          setStatusMessage('扩展程序未响应，请确认已安装')
        }
      }, 5000)
      
      // 立即发送测试消息
      console.log('📤 立即发送页面支持检查消息...')
      window.postMessage({ type: 'checkPageSupport' }, '*')
      
      // 1秒后再次尝试
      setTimeout(() => {
        if (extensionStatus === 'checking') {
          console.log('📤 再次发送页面支持检查消息...')
          window.postMessage({ type: 'checkPageSupport' }, '*')
        }
      }, 1000)
      
      // 清理超时
      return () => clearTimeout(timeoutId)
      
    } catch (error) {
      console.error('检查扩展程序失败:', error)
      setExtensionStatus('unavailable')
      setStatusMessage('扩展程序检查失败')
    }
  }
  
  /**
   * 初始化音频上下文
   */
  const initAudioContext = () => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
    }
    return audioContextRef.current
  }
  
  /**
   * 开始录制
   */
  const startRecording = async () => {
    try {
      if (isRecording) {
        console.warn('已经在录制中')
        return
      }
      
      setStatusMessage('正在启动录制...')
      
      // 发送开始录制消息到扩展程序
      window.postMessage({
        type: 'startRecording',
        sourceType: selectedSource
      }, '*')
      
    } catch (error) {
      console.error('开始录制失败:', error)
      setStatusMessage('开始录制失败: ' + error.message)
    }
  }
  
  /**
   * 停止录制
   */
  const stopRecording = () => {
    try {
      if (!isRecording) {
        console.warn('没有在录制中')
        return
      }
      
      setStatusMessage('正在停止录制...')
      
      // 发送停止录制消息到扩展程序
      window.postMessage({ type: 'stopRecording' }, '*')
      
    } catch (error) {
      console.error('停止录制失败:', error)
      setStatusMessage('停止录制失败: ' + error.message)
    }
  }
  
  /**
   * 处理录制开始
   */
  const handleRecordingStarted = (data) => {
    console.log('录制开始:', data)
    
    setIsRecording(true)
    setRecordingTime(0)
    setAudioChunks([])
    recordingStartTimeRef.current = Date.now()
    
    // 开始计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current)
    }
    
    recordingTimerRef.current = setInterval(() => {
      const elapsed = Date.now() - recordingStartTimeRef.current
      setRecordingTime(Math.floor(elapsed / 1000))
    }, 1000)
    
    setStatusMessage('正在录制系统音频...')
  }
  
  /**
   * 处理录制停止
   */
  const handleRecordingStopped = (data) => {
    console.log('录制停止:', data)
    
    setIsRecording(false)
    
    // 停止计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current)
      recordingTimerRef.current = null
    }
    
    // 处理音频数据
    if (data.audioBlob) {
      const url = URL.createObjectURL(data.audioBlob)
      setCurrentAudioUrl(url)
      
      // 添加到录音列表
      const name = recordingName || `系统音频_${new Date().toLocaleString()}`
      const newRecording = {
        id: Date.now(),
        name,
        url,
        blob: data.audioBlob,
        duration: data.duration || 0,
        createdAt: new Date().toISOString()
      }
      
      setAudioChunks(prev => [...prev, newRecording])
      setRecordingName('')
    }
    
    setStatusMessage('录制完成')
  }
  
  /**
   * 处理音频数据块
   */
  const handleAudioChunk = (data) => {
    if (isRecording) {
      console.log('收到音频数据块:', data)
      // 可以在这里处理实时音频数据
    }
  }
  
  /**
   * 处理扩展程序响应
   */
  const handleExtensionResponse = (data) => {
    console.log('扩展程序响应:', data)
    
    if (data.action === 'start') {
      if (data.success) {
        // 等待录制开始事件
      } else {
        setStatusMessage('启动录制失败: ' + data.error)
      }
    } else if (data.action === 'stop') {
      if (data.success) {
        // 等待录制停止事件
      } else {
        setStatusMessage('停止录制失败: ' + data.error)
      }
    }
  }
  
  /**
   * 处理页面支持状态
   */
  const handlePageSupport = (data) => {
    console.log('页面支持状态:', data)
    setPageSupport(data)
    
    if (data.supported) {
      setExtensionStatus('available')
      setStatusMessage('扩展程序已连接，可以开始录制')
    } else {
      setExtensionStatus('unavailable')
      setStatusMessage(data.error || '当前页面不支持音频录制')
    }
  }
  
  /**
   * 下载音频文件
   */
  const handleDownload = (recording) => {
    const link = document.createElement('a')
    link.href = recording.url
    link.download = `${recording.name}.wav`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  /**
   * 删除录音
   */
  const handleDelete = (id) => {
    setAudioChunks(prev => {
      const newChunks = prev.filter(chunk => chunk.id !== id)
      return newChunks
    })
  }
  
  /**
   * 格式化时间
   */
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  /**
   * 安装扩展程序
   */
  const installExtension = () => {
    // 打开扩展程序安装页面
    window.open('chrome://extensions/', '_blank')
  }
  
  // 初始化
  useEffect(() => {
    checkExtensionAvailability()
    
    // 监听来自扩展程序的消息
    const messageHandler = (event) => {
      if (event.source !== window) return
      
      const data = event.data
      if (!data || !data.type) return
      
      console.log('📨 收到扩展程序消息:', data.type, data)
      
      switch (data.type) {
        case 'contentScriptReady':
          console.log('🎉 内容脚本已准备就绪')
          console.log('📋 扩展程序版本:', data.version)
          setExtensionStatus('available')
          setStatusMessage('扩展程序已连接')
          break
          
        case 'recordingStarted':
          handleRecordingStarted(data)
          break
          
        case 'recordingStopped':
          handleRecordingStopped(data)
          break
          
        case 'audioChunk':
          handleAudioChunk(data)
          break
          
        case 'recordingResponse':
          handleExtensionResponse(data)
          break
          
        case 'pageSupport':
          handlePageSupport(data)
          break
          
        default:
          console.log('未知消息类型:', data.type)
      }
    }
    
    window.addEventListener('message', messageHandler)
    
    // 清理
    return () => {
      window.removeEventListener('message', messageHandler)
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
      }
      
      // 清理音频URL
      if (currentAudioUrl) {
        URL.revokeObjectURL(currentAudioUrl)
      }
      
      audioChunks.forEach(chunk => {
        if (chunk.url) {
          URL.revokeObjectURL(chunk.url)
        }
      })
    }
  }, [])

  return (
    <div className="recorder-container">
      <div className="recorder-header">
        <h2>系统音频录制</h2>
        <p>通过Chrome扩展程序捕获系统音频并录制</p>
        
        <div className="enhancement-notice">
          <h4>🚀 本次更新提升</h4>
          <ul>
            <li><strong>扩展集成：</strong>优化Chrome扩展程序通信机制</li>
            <li><strong>音频质量：</strong>支持高质量系统音频捕获</li>
            <li><strong>多源支持：</strong>支持标签页和桌面音频源选择</li>
            <li><strong>实时监控：</strong>提供扩展程序状态实时监控</li>
            <li><strong>错误处理：</strong>增强扩展程序连接和错误处理</li>
            <li><strong>用户界面：</strong>简化的扩展程序安装和使用流程</li>
          </ul>
        </div>
      </div>

      {/* 扩展程序状态 */}
      <div className="extension-status">
        <div className="status-header">
          <span className={`status-icon ${extensionStatus}`}>
            {extensionStatus === 'available' ? '✅' : extensionStatus === 'checking' ? '⏳' : '❌'}
          </span>
          <h3>扩展程序状态</h3>
        </div>
        <div className="status-content">
          {extensionStatus === 'checking' && (
            <p>正在检查扩展程序...</p>
          )}
          {extensionStatus === 'available' && (
            <p className="success">✅ 扩展程序已连接，可以开始录制</p>
          )}
          {extensionStatus === 'unavailable' && (
            <div className="unavailable-section">
              <p className="error">❌ 扩展程序未安装或不可用</p>
              <button 
                className="btn btn-primary"
                onClick={installExtension}
              >
                🧩 安装扩展程序
              </button>
              <div className="install-instructions">
                <h4>安装说明：</h4>
                <ol>
                  <li>下载扩展程序文件到本地</li>
                  <li>打开Chrome扩展程序管理页面 (chrome://extensions/)</li>
                  <li>开启"开发者模式"</li>
                  <li>点击"加载已解压的扩展程序"</li>
                  <li>选择extension文件夹</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 录制控制 */}
      {extensionStatus === 'available' && (
        <div className="recording-controls">
          <div className="status-display">
            <span className={`status-indicator ${isRecording ? 'recording' : 'idle'}`}></span>
            <span className="status-text">
              {statusMessage || (isRecording ? '正在录制...' : '准备就绪')}
            </span>
            {isRecording && (
              <span className="recording-time">
                ⏱️ {formatTime(recordingTime)}
              </span>
            )}
          </div>

          <div className="audio-source-selection">
            <h4>选择音频源：</h4>
            <div className="source-options">
              <label className="source-option">
                <input
                  type="radio"
                  name="audioSource"
                  value="tab"
                  checked={selectedSource === 'tab'}
                  onChange={(e) => setSelectedSource(e.target.value)}
                  disabled={isRecording}
                />
                <div className="option-content">
                  <span className="option-icon">📱</span>
                  <div className="option-info">
                    <div className="option-title">标签页音频</div>
                    <div className="option-desc">录制当前浏览器标签页的音频</div>
                  </div>
                </div>
              </label>
              
              <label className="source-option">
                <input
                  type="radio"
                  name="audioSource"
                  value="desktop"
                  checked={selectedSource === 'desktop'}
                  onChange={(e) => setSelectedSource(e.target.value)}
                  disabled={isRecording}
                />
                <div className="option-content">
                  <span className="option-icon">🖥️</span>
                  <div className="option-info">
                    <div className="option-title">桌面音频</div>
                    <div className="option-desc">录制完整的系统音频输出</div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <div className="input-group">
            <input
              type="text"
              placeholder="输入录音名称（可选）"
              value={recordingName}
              onChange={(e) => setRecordingName(e.target.value)}
              disabled={isRecording}
              className="recording-name-input"
            />
          </div>

          <div className="control-buttons">
            <button
              onClick={startRecording}
              disabled={isRecording || extensionStatus !== 'available'}
              className="btn btn-start"
            >
              🎵 开始录制
            </button>
            
            <button
              onClick={stopRecording}
              disabled={!isRecording}
              className="btn btn-stop"
            >
              ⏹️ 停止录制
            </button>
          </div>
        </div>
      )}

      {/* 录音列表 */}
      <div className="recordings-section">
        <h3>录音列表 ({audioChunks.length})</h3>
        {audioChunks.length === 0 ? (
          <p className="empty-message">暂无录音文件</p>
        ) : (
          <div className="recordings-grid">
            {audioChunks.map((recording) => (
              <div key={recording.id} className="recording-item">
                <div className="recording-info">
                  <h4 className="recording-name">{recording.name}</h4>
                  <p className="recording-date">
                    {new Date(recording.createdAt).toLocaleString()}
                  </p>
                  <p className="recording-duration">
                    时长: {formatTime(Math.floor(recording.duration / 1000))}
                  </p>
                </div>
                <audio
                  src={recording.url}
                  controls
                  className="recording-audio"
                />
                <div className="recording-actions">
                  <button
                    onClick={() => handleDownload(recording)}
                    className="btn btn-download"
                  >
                    📥 下载
                  </button>
                  <button
                    onClick={() => handleDelete(recording.id)}
                    className="btn btn-delete"
                  >
                    🗑️ 删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="instructions-section">
        <h3>使用说明</h3>
        <div className="instructions-content">
          <div className="instruction-item">
            <span className="step-number">1</span>
            <div className="instruction-content">
              <h4>安装扩展程序</h4>
              <p>确保已安装系统音频录制扩展程序</p>
            </div>
          </div>
          
          <div className="instruction-item">
            <span className="step-number">2</span>
            <div className="instruction-content">
              <h4>选择音频源</h4>
              <p>选择要录制的音频源类型（标签页或桌面）</p>
            </div>
          </div>
          
          <div className="instruction-item">
            <span className="step-number">3</span>
            <div className="instruction-content">
              <h4>开始录制</h4>
              <p>点击开始录制按钮，授权音频捕获权限</p>
            </div>
          </div>
          
          <div className="instruction-item">
            <span className="step-number">4</span>
            <div className="instruction-content">
              <h4>停止并保存</h4>
              <p>录制完成后点击停止，音频文件会自动保存</p>
            </div>
          </div>
        </div>
      </div>

      {/* 注意事项 */}
      <div className="notice-section">
        <h3>注意事项</h3>
        <div className="notice-content">
          <ul>
            <li>🔒 <strong>安全提醒：</strong>扩展程序需要音频捕获权限，请确保来源可信</li>
            <li>🌐 <strong>浏览器兼容：</strong>仅支持Chrome及其衍生浏览器</li>
            <li>🎵 <strong>音频质量：</strong>建议使用高质量音频源以获得最佳录制效果</li>
            <li>💾 <strong>文件格式：</strong>录音文件以WAV格式保存，支持高质量音频</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default SystemAudioRecorder
