/**
 * 音频质量检测工具
 * <AUTHOR>
 * @created 2025-01-15
 * @description 提供音频质量检测、设备兼容性检查和自适应配置功能
 */

class AudioQualityDetector {
  constructor() {
    this.supportedFormats = []
    this.maxSampleRate = 44100
    this.maxChannels = 1
    this.maxBitrate = 128000
    this.browserInfo = this.getBrowserInfo()
  }

  /**
   * 获取浏览器信息
   * @returns {Object} 浏览器信息对象
   */
  getBrowserInfo() {
    const userAgent = navigator.userAgent.toLowerCase()
    let browser = 'unknown'
    let version = 'unknown'

    if (userAgent.includes('chrome')) {
      browser = 'chrome'
      version = userAgent.match(/chrome\/(\d+)/)?.[1] || 'unknown'
    } else if (userAgent.includes('firefox')) {
      browser = 'firefox'
      version = userAgent.match(/firefox\/(\d+)/)?.[1] || 'unknown'
    } else if (userAgent.includes('safari')) {
      browser = 'safari'
      version = userAgent.match(/version\/(\d+)/)?.[1] || 'unknown'
    } else if (userAgent.includes('edge')) {
      browser = 'edge'
      version = userAgent.match(/edge\/(\d+)/)?.[1] || 'unknown'
    }

    return { browser, version, userAgent }
  }

  /**
   * 检测支持的音频格式
   * @returns {Array} 支持的格式列表
   */
  async detectSupportedFormats() {
    const formats = [
      'audio/webm;codecs=opus',
      'audio/ogg;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav',
      'audio/ogg'
    ]

    this.supportedFormats = []
    
    console.log('🔍 检测支持的音频格式...')
    
    for (const format of formats) {
      try {
        const isSupported = MediaRecorder.isTypeSupported(format)
        console.log(`${format}: ${isSupported ? '✅ 支持' : '❌ 不支持'}`)
        
        if (isSupported) {
          this.supportedFormats.push({
            format,
            quality: this.assessFormatQuality(format)
          })
        }
      } catch (error) {
        console.log(`${format}: ❌ 检测失败`)
      }
    }

    // 按质量排序
    this.supportedFormats.sort((a, b) => b.quality - a.quality)
    console.log('🎵 最佳音频格式:', this.supportedFormats[0]?.format)

    return this.supportedFormats
  }

  /**
   * 评估格式质量（0-100）
   * @param {string} format - 音频格式
   * @returns {number} 质量评分
   */
  assessFormatQuality(format) {
    if (format.includes('opus')) return 95
    if (format.includes('webm')) return 85
    if (format.includes('wav')) return 90
    if (format.includes('mp4')) return 75
    if (format.includes('ogg')) return 70
    return 50
  }

  /**
   * 检测最大支持的采样率
   * @returns {number} 最大采样率
   */
  async detectMaxSampleRate() {
    const sampleRates = [96000, 48000, 44100, 32000, 22050]
    
    console.log('🔍 检测最大支持的采样率...')
    
    for (const rate of sampleRates) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: rate,
            channelCount: 2
          }
        })
        
        stream.getTracks().forEach(track => track.stop())
        this.maxSampleRate = rate
        console.log(`✅ 支持 ${rate}Hz 采样率`)
        break
      } catch (error) {
        console.log(`❌ 不支持 ${rate}Hz 采样率`)
      }
    }

    console.log(`🎵 最大采样率: ${this.maxSampleRate}Hz`)
    return this.maxSampleRate
  }

  /**
   * 检测最大支持的声道数
   * @returns {number} 最大声道数
   */
  async detectMaxChannels() {
    const channelCounts = [2, 1] // 立体声和单声道
    
    console.log('🔍 检测最大支持的声道数...')
    
    for (const channels of channelCounts) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: this.maxSampleRate,
            channelCount: channels
          }
        })
        
        stream.getTracks().forEach(track => track.stop())
        this.maxChannels = channels
        console.log(`✅ 支持 ${channels} 声道`)
        break
      } catch (error) {
        console.log(`❌ 不支持 ${channels} 声道`)
      }
    }

    console.log(`🎵 最大声道数: ${this.maxChannels}`)
    return this.maxChannels
  }

  /**
   * 检测最大支持的比特率
   * @returns {number} 最大比特率
   */
  async detectMaxBitrate() {
    const bitrates = [320000, 256000, 192000, 128000, 96000]
    
    console.log('🔍 检测最大支持的比特率...')
    
    for (const bitrate of bitrates) {
      try {
        // 创建测试流
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: this.maxSampleRate,
            channelCount: this.maxChannels
          }
        })
        
        // 尝试创建MediaRecorder
        const bestFormat = this.supportedFormats[0]
        if (bestFormat) {
          const mediaRecorder = new MediaRecorder(stream, {
            mimeType: bestFormat.format,
            audioBitsPerSecond: bitrate
          })
          
          stream.getTracks().forEach(track => track.stop())
          this.maxBitrate = bitrate
          console.log(`✅ 支持 ${bitrate}bps 比特率`)
          break
        }
        
        stream.getTracks().forEach(track => track.stop())
      } catch (error) {
        console.log(`❌ 不支持 ${bitrate}bps 比特率`)
      }
    }

    console.log(`🎵 最大比特率: ${this.maxBitrate}bps`)
    return this.maxBitrate
  }

  /**
   * 获取最佳音频配置
   * @returns {Object} 最佳音频配置
   */
  async getOptimalAudioConfig() {
    console.log('🚀 开始检测最佳音频配置...')
    
    await this.detectSupportedFormats()
    await this.detectMaxSampleRate()
    await this.detectMaxChannels()
    await this.detectMaxBitrate()

    const bestFormat = this.supportedFormats[0]
    
    const config = {
      sampleRate: this.maxSampleRate,
      channelCount: this.maxChannels,
      sampleSize: 16,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      mediaRecorderOptions: {
        mimeType: bestFormat?.format || 'audio/webm',
        audioBitsPerSecond: this.maxBitrate
      },
      quality: {
        score: this.calculateQualityScore(),
        level: this.getQualityLevel(),
        specs: {
          format: bestFormat?.format || 'audio/webm',
          sampleRate: this.maxSampleRate,
          channels: this.maxChannels,
          bitrate: this.maxBitrate
        }
      }
    }

    console.log('🎯 最佳音频配置:', config)
    return config
  }

  /**
   * 计算质量评分 (0-100)
   * @returns {number} 质量评分
   */
  calculateQualityScore() {
    let score = 0
    
    // 采样率评分
    if (this.maxSampleRate >= 96000) score += 30
    else if (this.maxSampleRate >= 48000) score += 25
    else if (this.maxSampleRate >= 44100) score += 20
    else score += 10
    
    // 声道评分
    if (this.maxChannels >= 2) score += 20
    else score += 10
    
    // 比特率评分
    if (this.maxBitrate >= 320000) score += 30
    else if (this.maxBitrate >= 256000) score += 25
    else if (this.maxBitrate >= 192000) score += 20
    else score += 15
    
    // 格式评分
    if (this.supportedFormats.length > 0) {
      score += this.supportedFormats[0].quality * 0.2
    }
    
    return Math.min(100, Math.round(score))
  }

  /**
   * 获取质量等级
   * @returns {string} 质量等级
   */
  getQualityLevel() {
    const score = this.calculateQualityScore()
    if (score >= 90) return '🎵 专业级'
    if (score >= 75) return '🎧 高质量'
    if (score >= 60) return '📻 标准'
    return '📱 基础'
  }

  /**
   * 获取浏览器特定建议
   * @returns {Object} 浏览器建议
   */
  getBrowserRecommendations() {
    const { browser, version } = this.browserInfo
    const recommendations = {
      chrome: {
        bestFormat: 'audio/webm;codecs=opus',
        maxSampleRate: 48000,
        maxBitrate: 320000,
        notes: 'Chrome 完全支持高质量音频录制'
      },
      firefox: {
        bestFormat: 'audio/webm;codecs=opus',
        maxSampleRate: 48000,
        maxBitrate: 256000,
        notes: 'Firefox 支持高质量音频，但比特率略低'
      },
      safari: {
        bestFormat: 'audio/mp4',
        maxSampleRate: 44100,
        maxBitrate: 192000,
        notes: 'Safari 音频支持有限，建议使用 Chrome'
      },
      edge: {
        bestFormat: 'audio/webm;codecs=opus',
        maxSampleRate: 48000,
        maxBitrate: 320000,
        notes: 'Edge 完全支持高质量音频录制'
      }
    }

    return recommendations[browser] || recommendations.chrome
  }
}

export default AudioQualityDetector