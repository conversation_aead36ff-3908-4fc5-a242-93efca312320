/*
 * 音频录制应用样式文件
 * <AUTHOR>
 * @created 2025-01-11
 * @description 为三种音频录制场景提供统一的UI样式
 */

/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

/* 主应用容器 */
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

/* 应用头部 */
.app-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  max-width: 200px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  background: #f0f0f0;
  color: #333;
}

.tab-button.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 录制器容器 */
.recorder-container {
  padding: 30px;
}

.recorder-header {
  margin-bottom: 30px;
  text-align: center;
}

.recorder-header h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
}

.recorder-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 录制控制区域 */
.recording-controls {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 30px;
  border: 2px solid #e9ecef;
}

/* 状态显示 */
.status-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
  animation: pulse 2s infinite;
}

.status-indicator.idle {
  background: #6c757d;
  animation: none;
}

.status-indicator.recording {
  background: #dc3545;
}

.status-indicator.stopped {
  background: #28a745;
  animation: none;
}

.status-indicator.acquiring_media {
  background: #ffc107;
}

.status-indicator.permission_denied {
  background: #dc3545;
  animation: none;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 500;
  color: #333;
}

.recording-time {
  color: #dc3545;
  font-weight: 600;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 输入组件 */
.input-group {
  margin-bottom: 20px;
}

.recording-name-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.recording-name-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.recording-name-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* 控制按钮 */
.control-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-start {
  background: #28a745;
  color: white;
}

.btn-start:hover:not(:disabled) {
  background: #218838;
}

.btn-stop {
  background: #dc3545;
  color: white;
}

.btn-stop:hover:not(:disabled) {
  background: #c82333;
}

.btn-mute {
  background: #ffc107;
  color: #212529;
}

.btn-mute:hover:not(:disabled) {
  background: #e0a800;
}

.btn-clear {
  background: #6c757d;
  color: white;
}

.btn-clear:hover:not(:disabled) {
  background: #5a6268;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.btn-download {
  background: #17a2b8;
  color: white;
  padding: 8px 16px;
  font-size: 0.9rem;
  min-width: auto;
}

.btn-download:hover:not(:disabled) {
  background: #138496;
}

.btn-delete {
  background: #dc3545;
  color: white;
  padding: 8px 16px;
  font-size: 0.9rem;
  min-width: auto;
}

.btn-delete:hover:not(:disabled) {
  background: #c82333;
}

/* 音频预览 */
.audio-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
  border: 2px solid #e9ecef;
}

.audio-preview h3 {
  margin-bottom: 15px;
  color: #333;
}

.audio-player {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}
/* 视频预览样式 */
.video-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
  border: 2px solid #e9ecef;
}

.video-preview h3 {
  margin-bottom: 15px;
  color: #333;
}

.video-player {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recording-video {
  border-radius: 8px;
  background: #000;
}

/* 录音列表 */
.recordings-list {
  margin-top: 30px;
}

.recordings-list h3 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.empty-message {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.recordings-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.recording-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.recording-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.recording-info {
  margin-bottom: 15px;
}

.recording-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  word-break: break-word;
}

.recording-date {
  color: #6c757d;
  font-size: 0.9rem;
}

.recording-audio {
  width: 100%;
  margin-bottom: 15px;
}

.recording-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 系统音频录制组件特殊样式 */
.limitation-notice {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.notice-icon {
  font-size: 1.5rem;
}

.notice-header h3 {
  color: #856404;
  margin: 0;
}

.notice-content p {
  color: #856404;
  font-weight: 600;
  margin-bottom: 10px;
}

.notice-content ul {
  color: #856404;
  padding-left: 20px;
}

.notice-content li {
  margin-bottom: 8px;
}

/* 解决方案网格 */
.solutions-section {
  margin-bottom: 40px;
}

.solutions-section h3 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-size: 1.5rem;
}

.solutions-grid {
  display: grid;
  gap: 25px;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.solution-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.solution-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.solution-card.selected {
  border-color: #667eea;
  background: #f8f9ff;
}

.solution-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.solution-icon {
  font-size: 1.8rem;
}

.solution-header h4 {
  flex: 1;
  color: #333;
  margin: 0;
}

.difficulty-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty-badge.easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-badge.medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-badge.hard {
  background: #f8d7da;
  color: #721c24;
}

.solution-content p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.pros-cons {
  display: grid;
  gap: 15px;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 20px;
}

.pros h5, .cons h5 {
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.pros ul, .cons ul {
  font-size: 0.85rem;
  padding-left: 15px;
}

.pros li, .cons li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.solution-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.solution-actions .btn {
  font-size: 0.9rem;
  padding: 10px 16px;
  min-width: auto;
}

/* 兼容性说明 */
.compatibility-notice {
  background: #e7f3ff;
  border: 2px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.compatibility-notice strong {
  color: #0056b3;
}

.compatibility-notice ul {
  margin-top: 10px;
  padding-left: 20px;
}

.compatibility-notice li {
  color: #0056b3;
  margin-bottom: 5px;
}

/* 使用说明 */
.instructions {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.instructions h3 {
  color: #333;
  margin-bottom: 15px;
}

.instructions ol {
  padding-left: 20px;
  color: #666;
}

.instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.instructions p {
  margin-top: 15px;
  color: #666;
  font-style: italic;
}
/* 音频录制专用样式 */
.audio-only-notice {
  background: #d4edda;
  border: 2px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.audio-only-notice h3 {
  color: #155724;
  margin-bottom: 10px;
}

.audio-only-notice p {
  color: #155724;
  margin: 0;
  line-height: 1.5;
}

.important-tips {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.important-tips h4 {
  color: #856404;
  margin-bottom: 10px;
}

.important-tips ul {
  color: #856404;
  padding-left: 20px;
  margin: 0;
}

.important-tips li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 步骤指南样式 */
.step-by-step {
  display: grid;
  gap: 20px;
  margin-top: 15px;
}

.step {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  align-items: flex-start;
}

.step-number {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.step-content h4 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1rem;
}

.step-content p {
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 故障排除指南样式 */
.troubleshooting-guide {
  background: #f8d7da;
  border: 2px solid #f5c6cb;
  border-radius: 12px;
  padding: 25px;
  margin: 30px 0;
}

.troubleshooting-guide h3 {
  color: #721c24;
  margin-bottom: 20px;
  text-align: center;
}

.solution-cards {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.solution-card {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.solution-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.solution-card.urgent {
  border-color: #28a745;
  background: #f8fff9;
}

.solution-card h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
}

.solution-card p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.solution-card ul {
  color: #666;
  padding-left: 20px;
  margin: 0;
}

.solution-card li {
  margin-bottom: 5px;
  line-height: 1.4;
}

/* 浏览器提示样式 */
.browser-specific-tips {
  margin: 30px 0;
}

.browser-specific-tips h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.browser-tips-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.browser-tip {
  border-radius: 8px;
  padding: 20px;
  border: 2px solid;
}

.browser-tip.chrome {
  background: #e8f5e8;
  border-color: #28a745;
}

.browser-tip.firefox {
  background: #fff3cd;
  border-color: #ffc107;
}

.browser-tip.safari {
  background: #f8d7da;
  border-color: #dc3545;
}

.browser-tip h4 {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.browser-tip.chrome h4 {
  color: #155724;
}

.browser-tip.firefox h4 {
  color: #856404;
}

.browser-tip.safari h4 {
  color: #721c24;
}

.tip-content p {
  font-weight: 600;
  margin-bottom: 10px;
}

.browser-tip.chrome .tip-content p {
  color: #155724;
}

.browser-tip.firefox .tip-content p {
  color: #856404;
}

.browser-tip.safari .tip-content p {
  color: #721c24;
}

.tip-content ul {
  padding-left: 20px;
  margin: 0;
}

.browser-tip.chrome .tip-content ul {
  color: #155724;
}

.browser-tip.firefox .tip-content ul {
  color: #856404;
}

.browser-tip.safari .tip-content ul {
  color: #721c24;
}

.tip-content li {
  margin-bottom: 5px;
  line-height: 1.4;
}
/* 错误提示样式 */
.error-notice {
  background: #f8d7da;
  border: 2px solid #f5c6cb;
  border-radius: 12px;
  padding: 25px;
  margin: 20px 0;
  text-align: center;
}

.error-notice h3 {
  color: #721c24;
  margin-bottom: 15px;
}

.error-message {
  color: #721c24;
  font-weight: 600;
  background: white;
  padding: 10px;
  border-radius: 6px;
  margin: 15px 0;
  border: 1px solid #f5c6cb;
}

.solutions {
  text-align: left;
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #f5c6cb;
}

.solutions h4 {
  color: #721c24;
  margin-bottom: 10px;
}

.solutions ul {
  color: #721c24;
  padding-left: 20px;
}

.solutions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.retry-section {
  margin-top: 20px;
}
.important-notes {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.important-notes p {
  color: #856404;
  font-weight: 600;
  margin-bottom: 10px;
}

.important-notes ul {
  color: #856404;
  padding-left: 20px;
  margin: 0;
}

.important-notes li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 实现指南 */
.implementation-guide {
  margin-bottom: 40px;
}

.implementation-guide h3 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
}

.guide-content {
  max-width: 800px;
  margin: 0 auto;
}

.step {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.step-content h4 {
  color: #333;
  margin-bottom: 8px;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

/* 演示和资源部分 */
.demo-section, .resources-section {
  margin-bottom: 30px;
}

.demo-section h3, .resources-section h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.demo-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.demo-content ol {
  padding-left: 20px;
  color: #666;
  margin-bottom: 15px;
}

.demo-content li {
  margin-bottom: 8px;
}

.demo-actions {
  text-align: center;
  margin-top: 15px;
}

.resources-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.resource-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.resource-item h4 {
  color: #333;
  margin-bottom: 15px;
}

.resource-item ul {
  padding-left: 20px;
}

.resource-item li {
  margin-bottom: 8px;
}

.resource-item a {
  color: #667eea;
  text-decoration: none;
}

.resource-item a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 15px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 8px;
  }

  .tab-button {
    max-width: none;
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* 音频测试页面样式 */
.audio-test-page {
  padding: 30px;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.test-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.test-instructions {
  background: #e7f3ff;
  border: 2px solid #b3d9ff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.test-instructions h3 {
  color: #0056b3;
  margin-bottom: 15px;
}

.test-instructions ol {
  color: #0056b3;
  padding-left: 20px;
}

.test-instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.audio-controls {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
}

.audio-controls h3 {
  color: #333;
  margin-bottom: 20px;
}

.track-selector {
  margin-bottom: 25px;
}

.track-selector h4 {
  color: #333;
  margin-bottom: 10px;
}

.track-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.playback-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.current-track-info {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.current-track-info h4 {
  color: #333;
  margin-bottom: 8px;
}

.current-track-info p {
  margin-bottom: 5px;
  color: #666;
}

.playback-status {
  margin-top: 10px;
  font-weight: 500;
}

.status-playing {
  color: #28a745;
}

.status-stopped {
  color: #6c757d;
}

.browser-tips {
  margin-bottom: 30px;
}

.browser-tips h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.tips-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.tip-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.tip-item h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.tip-item ul {
  padding-left: 20px;
  color: #666;
}

.tip-item li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.troubleshooting {
  margin-bottom: 30px;
}

.troubleshooting h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.troubleshooting-list {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.issue {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
}

.issue h4 {
  color: #856404;
  margin-bottom: 10px;
}

.issue ul {
  padding-left: 20px;
  color: #856404;
}

.issue li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.test-footer {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.test-footer p {
  color: #666;
  margin: 0;
  font-style: italic;
}

/* 测试页面响应式设计 */
@media (max-width: 768px) {
  .track-buttons {
    flex-direction: column;
  }

  .playback-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .tips-grid {
    grid-template-columns: 1fr;
  }

  .troubleshooting-list {
    grid-template-columns: 1fr;
  }
}
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  .recordings-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .step {
    flex-direction: column;
    text-align: center;
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }
}

/* 浏览器兼容性检查组件样式 */
.compatibility-checker {
  padding: 30px;
}

.compatibility-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.compatibility-header h3 {
  color: #333;
  margin: 0;
  font-size: 1.5rem;
}

.browser-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.browser-name {
  font-weight: 600;
  color: #667eea;
  font-size: 1.1rem;
}

.browser-version {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #6c757d;
}

.support-grid {
  display: grid;
  gap: 15px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  margin-bottom: 30px;
}

.support-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.support-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.support-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.support-label {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.support-status {
  font-size: 0.9rem;
  color: #6c757d;
}

.audio-formats {
  margin-bottom: 30px;
}

.audio-formats h4 {
  color: #333;
  margin-bottom: 15px;
}

.format-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.format-tag {
  background: #e7f3ff;
  color: #0056b3;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #b3d9ff;
}

.no-formats {
  color: #dc3545;
  font-style: italic;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.recommendations {
  margin-bottom: 25px;
}

.recommendations h4 {
  color: #333;
  margin-bottom: 15px;
}

.recommendation-list {
  list-style: none;
  padding: 0;
}

.recommendation-item {
  background: #fff3cd;
  color: #856404;
  padding: 10px 15px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
  position: relative;
}

.recommendation-item::before {
  content: "💡";
  margin-right: 8px;
}

.compatibility-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.compatibility-footer .note {
  color: #6c757d;
  font-style: italic;
  margin: 0;
}

/* 兼容性检查响应式设计 */
@media (max-width: 768px) {
  .compatibility-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .support-grid {
    grid-template-columns: 1fr;
  }

  .format-list {
    justify-content: center;
  }
}

/* 提升说明样式 */
.enhancement-notice {
  background: linear-gradient(135deg, #e7f3ff 0%, #d4edda 100%);
  border: 2px solid #0056b3;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
}

.enhancement-notice::before {
  content: "🚀";
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5rem;
  opacity: 0.7;
}

.enhancement-notice h4 {
  color: #0056b3;
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.enhancement-notice ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.enhancement-notice li {
  background: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  padding: 10px 15px;
  border-radius: 6px;
  border-left: 4px solid #28a745;
  color: #155724;
  font-weight: 500;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.enhancement-notice li:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enhancement-notice li strong {
  color: #0056b3;
  font-weight: 600;
}

.enhancement-notice li:last-child {
  margin-bottom: 0;
}

/* 提升说明响应式设计 */
@media (max-width: 768px) {
  .enhancement-notice {
    padding: 15px;
  }
  
  .enhancement-notice h4 {
    font-size: 1.1rem;
    text-align: center;
  }
  
  .enhancement-notice li {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}